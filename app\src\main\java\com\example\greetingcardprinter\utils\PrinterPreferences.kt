package com.example.greetingcardprinter.utils

import android.content.Context
import android.content.SharedPreferences
import com.example.greetingcardprinter.models.PrinterInfo

/**
 * 打印机配置信息本地存储工具类
 */
class PrinterPreferences(context: Context) {
    private val prefs: SharedPreferences = context.getSharedPreferences(
        PREF_NAME,
        Context.MODE_PRIVATE
    )

    /**
     * 保存最后使用的打印机信息
     */
    fun saveLastUsedPrinter(printerInfo: PrinterInfo) {
        prefs.edit().apply {
            putString(KEY_PRINTER_ID, printerInfo.id)
            putString(KEY_PRINTER_SERVICE_NAME, printerInfo.serviceName)
            putString(KEY_PRINTER_DESCRIPTION, printerInfo.description)
            putBoolean(KEY_PRINTER_IS_DEFAULT, printerInfo.isDefault)
            putBoolean(KEY_PRINTER_IS_CONNECTED, printerInfo.isConnected)
            putString(KEY_PRINTER_HOST_ADDRESS, printerInfo.hostAddress)
            apply()
        }
    }

    /**
     * 获取最后使用的打印机信息
     * @return 如果存在则返回PrinterInfo对象，否则返回null
     */
    fun getLastUsedPrinter(): PrinterInfo? {
        val id = prefs.getString(KEY_PRINTER_ID, null) ?: return null
        val serviceName = prefs.getString(KEY_PRINTER_SERVICE_NAME, null) ?: return null

        return PrinterInfo(
            id = id,
            serviceName = serviceName,
            description = prefs.getString(KEY_PRINTER_DESCRIPTION, "") ?: "",
            isDefault = prefs.getBoolean(KEY_PRINTER_IS_DEFAULT, false),
            isConnected = prefs.getBoolean(KEY_PRINTER_IS_CONNECTED, false),
            hostAddress = prefs.getString(KEY_PRINTER_HOST_ADDRESS, "") ?: ""
        )
    }

    /**
     * 清除保存的打印机信息
     */
    fun clearLastUsedPrinter() {
        prefs.edit().apply {
            remove(KEY_PRINTER_ID)
            remove(KEY_PRINTER_SERVICE_NAME)
            remove(KEY_PRINTER_DESCRIPTION)
            remove(KEY_PRINTER_IS_DEFAULT)
            remove(KEY_PRINTER_IS_CONNECTED)
            remove(KEY_PRINTER_HOST_ADDRESS)
            apply()
        }
    }

    companion object {
        private const val PREF_NAME = "printer_preferences"
        private const val KEY_PRINTER_ID = "printer_id"
        private const val KEY_PRINTER_SERVICE_NAME = "printer_service_name"
        private const val KEY_PRINTER_DESCRIPTION = "printer_description"
        private const val KEY_PRINTER_IS_DEFAULT = "printer_is_default"
        private const val KEY_PRINTER_IS_CONNECTED = "printer_is_connected"
        private const val KEY_PRINTER_HOST_ADDRESS = "printer_host_address"
    }
}