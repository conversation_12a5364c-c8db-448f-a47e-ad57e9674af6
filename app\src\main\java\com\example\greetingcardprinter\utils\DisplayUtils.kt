package com.example.greetingcardprinter.utils

import android.content.Context
import android.util.DisplayMetrics
import android.util.TypedValue

/**
 * 显示相关的工具函数
 */

/**
 * 将毫米转换为像素
 * @param context 上下文
 * @return 对应的像素值
 */
fun Float.mmToPx(context: Context): Float {
    val metrics = context.resources.displayMetrics
    // 1英寸 = 25.4毫米
    val mmToInch = 1.0f / 25.4f
    // 将毫米转换为英寸，然后乘以屏幕密度
    return this * mmToInch * metrics.densityDpi
}

/**
 * 将像素转换为毫米
 * @param context 上下文
 * @return 对应的毫米值
 */
fun Float.pxToMm(context: Context): Float {
    val metrics = context.resources.displayMetrics
    // 1英寸 = 25.4毫米
    val inchToMm = 25.4f
    // 将像素转换为英寸，然后乘以25.4得到毫米
    return this / metrics.densityDpi * inchToMm
}

/**
 * 将dp转换为像素
 * @param context 上下文
 * @return 对应的像素值
 */
fun Float.dpToPx(context: Context): Float {
    return TypedValue.applyDimension(
        TypedValue.COMPLEX_UNIT_DIP,
        this,
        context.resources.displayMetrics
    )
} 