<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    tools:context=".activities.ImagePickerActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 选择图片按钮 -->
        <Button
            android:id="@+id/btnSelectImage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="选择图片"
            android:drawableLeft="@android:drawable/ic_menu_gallery"
            android:layout_marginBottom="16dp" />

        <!-- 图片预览 -->
        <ImageView
            android:id="@+id/imagePreview"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:scaleType="fitCenter"
            android:background="#E0E0E0"
            android:visibility="gone"
            android:layout_marginBottom="16dp"
            tools:visibility="visible" />

        <!-- 图片尺寸信息 -->
        <TextView
            android:id="@+id/tvImageSize"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="尺寸信息"
            android:textSize="14sp"
            android:visibility="gone"
            android:layout_marginBottom="16dp"
            tools:visibility="visible" />

        <!-- 图片位置选择 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="图片位置"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <Spinner
            android:id="@+id/spinnerPosition"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp" />

        <!-- 位置描述 -->
        <TextView
            android:id="@+id/tvPositionDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="图片将显示在文本上方"
            android:textSize="14sp"
            android:textColor="#757575"
            android:layout_marginBottom="16dp" />

        <!-- 图片宽度调整 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="图片宽度"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <SeekBar
                android:id="@+id/seekBarWidth"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:max="100"
                android:progress="70" />

            <TextView
                android:id="@+id/tvWidthPercentage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="70%"
                android:layout_marginStart="8dp"
                android:minWidth="50dp" />
        </LinearLayout>

        <!-- 图片高度信息 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="预计高度"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <TextView
            android:id="@+id/tvHeightMm"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="50.0 毫米"
            android:textSize="14sp"
            android:layout_marginBottom="32dp" />

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btnCancel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="取消"
                android:layout_marginEnd="8dp"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

            <Button
                android:id="@+id/btnConfirm"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="确认"
                android:enabled="false" />
        </LinearLayout>
    </LinearLayout>
</ScrollView> 