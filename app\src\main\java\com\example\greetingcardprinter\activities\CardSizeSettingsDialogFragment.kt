package com.example.greetingcardprinter.activities

import android.graphics.Typeface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import androidx.fragment.app.DialogFragment
import com.example.greetingcardprinter.R
import com.example.greetingcardprinter.databinding.DialogCardSizeSettingsBinding

class CardSizeSettingsDialogFragment : DialogFragment() {

    private var _binding: DialogCardSizeSettingsBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = DialogCardSizeSettingsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 设置字体
        val typeface = Typeface.createFromAsset(requireContext().assets, "fonts/qiantuxianmoti.ttf")
        binding.dialogTitle.typeface = typeface
        binding.cardSizeInfoText.typeface = typeface
        
        // 设置关闭按钮点击事件
        binding.closeButton.setOnClickListener {
            dismiss()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        const val TAG = "CardSizeSettingsDialog"
    }
} 