package com.example.greetingcardprinter.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.greetingcardprinter.databinding.ItemTemplateBinding
import com.example.greetingcardprinter.models.Template

/**
 * 模板列表适配器
 */
class TemplateAdapter(
    private val onTemplateClick: (Template) -> Unit,
    private val onEditClick: (Template) -> Unit,
    private val onDeleteClick: (Template) -> Unit
) : ListAdapter<Template, TemplateAdapter.TemplateViewHolder>(TemplateDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TemplateViewHolder {
        val binding = ItemTemplateBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return TemplateViewHolder(binding)
    }

    override fun onBindViewHolder(holder: TemplateViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class TemplateViewHolder(
        private val binding: ItemTemplateBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(template: Template) {
            binding.apply {
                // 设置模板名称
                templateNameText.text = template.name
                
                // 设置内容预览
                templateContentText.text = template.getContentPreview()
                
                // 设置创建时间
                templateTimeText.text = "创建于 ${template.getFormattedCreatedTime()}"
                
                // 设置模板信息（字体大小和行间距）
                templateInfoText.text = "字号${template.fontSize.toInt()} 行距${template.lineSpacing}"
                
                // 设置点击事件
                root.setOnClickListener {
                    onTemplateClick(template)
                }
                
                editButton.setOnClickListener {
                    onEditClick(template)
                }
                
                deleteButton.setOnClickListener {
                    onDeleteClick(template)
                }
            }
        }
    }

    /**
     * DiffUtil回调，用于高效更新列表
     */
    private class TemplateDiffCallback : DiffUtil.ItemCallback<Template>() {
        override fun areItemsTheSame(oldItem: Template, newItem: Template): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Template, newItem: Template): Boolean {
            return oldItem == newItem
        }
    }
}
