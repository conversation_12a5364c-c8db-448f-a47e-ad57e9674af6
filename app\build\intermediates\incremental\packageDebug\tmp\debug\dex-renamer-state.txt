#Fri Aug 08 18:55:42 CST 2025
base.0=D\:\\CardPrint3\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
base.1=D\:\\CardPrint3\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.2=D\:\\CardPrint3\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\13\\classes.dex
base.3=D\:\\CardPrint3\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\4\\classes.dex
base.4=D\:\\CardPrint3\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\5\\classes.dex
base.5=D\:\\CardPrint3\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\6\\classes.dex
base.6=D\:\\CardPrint3\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\8\\classes.dex
base.7=D\:\\CardPrint3\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\9\\classes.dex
path.0=classes.dex
path.1=0/classes.dex
path.2=13/classes.dex
path.3=4/classes.dex
path.4=5/classes.dex
path.5=6/classes.dex
path.6=8/classes.dex
path.7=9/classes.dex
renamed.0=classes.dex
renamed.1=classes2.dex
renamed.2=classes3.dex
renamed.3=classes4.dex
renamed.4=classes5.dex
renamed.5=classes6.dex
renamed.6=classes7.dex
renamed.7=classes8.dex
