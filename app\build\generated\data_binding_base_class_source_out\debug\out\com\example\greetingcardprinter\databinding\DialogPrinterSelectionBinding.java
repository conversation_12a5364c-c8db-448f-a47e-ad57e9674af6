// Generated by view binder compiler. Do not edit!
package com.example.greetingcardprinter.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.greetingcardprinter.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogPrinterSelectionBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button addManualButton;

  @NonNull
  public final Button cancelButton;

  @NonNull
  public final TextView emptyView;

  @NonNull
  public final RecyclerView printerRecyclerView;

  @NonNull
  public final Button refreshButton;

  private DialogPrinterSelectionBinding(@NonNull LinearLayout rootView,
      @NonNull Button addManualButton, @NonNull Button cancelButton, @NonNull TextView emptyView,
      @NonNull RecyclerView printerRecyclerView, @NonNull Button refreshButton) {
    this.rootView = rootView;
    this.addManualButton = addManualButton;
    this.cancelButton = cancelButton;
    this.emptyView = emptyView;
    this.printerRecyclerView = printerRecyclerView;
    this.refreshButton = refreshButton;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogPrinterSelectionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogPrinterSelectionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_printer_selection, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogPrinterSelectionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addManualButton;
      Button addManualButton = ViewBindings.findChildViewById(rootView, id);
      if (addManualButton == null) {
        break missingId;
      }

      id = R.id.cancelButton;
      Button cancelButton = ViewBindings.findChildViewById(rootView, id);
      if (cancelButton == null) {
        break missingId;
      }

      id = R.id.emptyView;
      TextView emptyView = ViewBindings.findChildViewById(rootView, id);
      if (emptyView == null) {
        break missingId;
      }

      id = R.id.printerRecyclerView;
      RecyclerView printerRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (printerRecyclerView == null) {
        break missingId;
      }

      id = R.id.refreshButton;
      Button refreshButton = ViewBindings.findChildViewById(rootView, id);
      if (refreshButton == null) {
        break missingId;
      }

      return new DialogPrinterSelectionBinding((LinearLayout) rootView, addManualButton,
          cancelButton, emptyView, printerRecyclerView, refreshButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
