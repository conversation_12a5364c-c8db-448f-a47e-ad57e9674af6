// Generated by view binder compiler. Do not edit!
package com.example.greetingcardprinter.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.greetingcardprinter.R;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogTemplateEditBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button cancelButton;

  @NonNull
  public final Button saveButton;

  @NonNull
  public final TextInputEditText templateContentEditText;

  @NonNull
  public final TextInputEditText templateNameEditText;

  private DialogTemplateEditBinding(@NonNull LinearLayout rootView, @NonNull Button cancelButton,
      @NonNull Button saveButton, @NonNull TextInputEditText templateContentEditText,
      @NonNull TextInputEditText templateNameEditText) {
    this.rootView = rootView;
    this.cancelButton = cancelButton;
    this.saveButton = saveButton;
    this.templateContentEditText = templateContentEditText;
    this.templateNameEditText = templateNameEditText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogTemplateEditBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogTemplateEditBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_template_edit, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogTemplateEditBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cancelButton;
      Button cancelButton = ViewBindings.findChildViewById(rootView, id);
      if (cancelButton == null) {
        break missingId;
      }

      id = R.id.saveButton;
      Button saveButton = ViewBindings.findChildViewById(rootView, id);
      if (saveButton == null) {
        break missingId;
      }

      id = R.id.templateContentEditText;
      TextInputEditText templateContentEditText = ViewBindings.findChildViewById(rootView, id);
      if (templateContentEditText == null) {
        break missingId;
      }

      id = R.id.templateNameEditText;
      TextInputEditText templateNameEditText = ViewBindings.findChildViewById(rootView, id);
      if (templateNameEditText == null) {
        break missingId;
      }

      return new DialogTemplateEditBinding((LinearLayout) rootView, cancelButton, saveButton,
          templateContentEditText, templateNameEditText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
