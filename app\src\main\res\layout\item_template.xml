<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    android:foreground="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 模板标题和操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/templateNameText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:maxLines="1"
                android:ellipsize="end"
                tools:text="生日祝福模板" />

            <!-- 操作按钮 -->
            <ImageButton
                android:id="@+id/editButton"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_edit"
                android:contentDescription="编辑模板"
                android:layout_marginEnd="8dp" />

            <ImageButton
                android:id="@+id/deleteButton"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_delete"
                android:contentDescription="删除模板"
                app:tint="@color/error" />

        </LinearLayout>

        <!-- 模板内容预览 -->
        <TextView
            android:id="@+id/templateContentText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textSize="14sp"
            android:textColor="@color/text_secondary"
            android:maxLines="3"
            android:ellipsize="end"
            android:lineSpacingExtra="2dp"
            tools:text="亲爱的朋友，\n祝你生日快乐！\n愿你的每一天都充满阳光和快乐..." />

        <!-- 模板信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="12dp"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/templateTimeText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textSize="12sp"
                android:textColor="@color/text_hint"
                tools:text="创建于 2024-01-20 14:30" />

            <TextView
                android:id="@+id/templateInfoText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="@color/text_hint"
                android:background="@drawable/bg_template_info"
                android:paddingHorizontal="8dp"
                android:paddingVertical="2dp"
                tools:text="字号20 行距1.5" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
