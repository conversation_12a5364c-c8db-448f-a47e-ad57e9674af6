// Generated by view binder compiler. Do not edit!
package com.example.greetingcardprinter.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.greetingcardprinter.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button addImageButton;

  @NonNull
  public final ImageButton alignCenterButton;

  @NonNull
  public final ImageButton alignLeftButton;

  @NonNull
  public final ImageButton alignRightButton;

  @NonNull
  public final LinearLayout bottomButtonsLayout;

  @NonNull
  public final View cardSizeIndicator;

  @NonNull
  public final ImageButton clearTextButton;

  @NonNull
  public final TextView copiesCountText;

  @NonNull
  public final ImageButton copiesDecreaseButton;

  @NonNull
  public final ImageButton copiesIncreaseButton;

  @NonNull
  public final EditText editText;

  @NonNull
  public final ScrollView editorScrollView;

  @NonNull
  public final ImageButton fontSizeDecreaseButton;

  @NonNull
  public final ImageButton fontSizeIncreaseButton;

  @NonNull
  public final TextView fontSizeText;

  @NonNull
  public final LinearLayout formatControlsContent;

  @NonNull
  public final ImageView formatControlsExpandIcon;

  @NonNull
  public final LinearLayout formatControlsHeader;

  @NonNull
  public final LinearLayout formatControlsLayout;

  @NonNull
  public final ImageView imagePreview;

  @NonNull
  public final FrameLayout imagePreviewContainer;

  @NonNull
  public final ImageButton lineSpacingDecreaseButton;

  @NonNull
  public final ImageButton lineSpacingIncreaseButton;

  @NonNull
  public final TextView lineSpacingText;

  @NonNull
  public final ImageView previewImageView;

  @NonNull
  public final Button printButton;

  @NonNull
  public final ImageButton removeImageButton;

  @NonNull
  public final Toolbar toolbar;

  private ActivityMainBinding(@NonNull ConstraintLayout rootView, @NonNull Button addImageButton,
      @NonNull ImageButton alignCenterButton, @NonNull ImageButton alignLeftButton,
      @NonNull ImageButton alignRightButton, @NonNull LinearLayout bottomButtonsLayout,
      @NonNull View cardSizeIndicator, @NonNull ImageButton clearTextButton,
      @NonNull TextView copiesCountText, @NonNull ImageButton copiesDecreaseButton,
      @NonNull ImageButton copiesIncreaseButton, @NonNull EditText editText,
      @NonNull ScrollView editorScrollView, @NonNull ImageButton fontSizeDecreaseButton,
      @NonNull ImageButton fontSizeIncreaseButton, @NonNull TextView fontSizeText,
      @NonNull LinearLayout formatControlsContent, @NonNull ImageView formatControlsExpandIcon,
      @NonNull LinearLayout formatControlsHeader, @NonNull LinearLayout formatControlsLayout,
      @NonNull ImageView imagePreview, @NonNull FrameLayout imagePreviewContainer,
      @NonNull ImageButton lineSpacingDecreaseButton,
      @NonNull ImageButton lineSpacingIncreaseButton, @NonNull TextView lineSpacingText,
      @NonNull ImageView previewImageView, @NonNull Button printButton,
      @NonNull ImageButton removeImageButton, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.addImageButton = addImageButton;
    this.alignCenterButton = alignCenterButton;
    this.alignLeftButton = alignLeftButton;
    this.alignRightButton = alignRightButton;
    this.bottomButtonsLayout = bottomButtonsLayout;
    this.cardSizeIndicator = cardSizeIndicator;
    this.clearTextButton = clearTextButton;
    this.copiesCountText = copiesCountText;
    this.copiesDecreaseButton = copiesDecreaseButton;
    this.copiesIncreaseButton = copiesIncreaseButton;
    this.editText = editText;
    this.editorScrollView = editorScrollView;
    this.fontSizeDecreaseButton = fontSizeDecreaseButton;
    this.fontSizeIncreaseButton = fontSizeIncreaseButton;
    this.fontSizeText = fontSizeText;
    this.formatControlsContent = formatControlsContent;
    this.formatControlsExpandIcon = formatControlsExpandIcon;
    this.formatControlsHeader = formatControlsHeader;
    this.formatControlsLayout = formatControlsLayout;
    this.imagePreview = imagePreview;
    this.imagePreviewContainer = imagePreviewContainer;
    this.lineSpacingDecreaseButton = lineSpacingDecreaseButton;
    this.lineSpacingIncreaseButton = lineSpacingIncreaseButton;
    this.lineSpacingText = lineSpacingText;
    this.previewImageView = previewImageView;
    this.printButton = printButton;
    this.removeImageButton = removeImageButton;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addImageButton;
      Button addImageButton = ViewBindings.findChildViewById(rootView, id);
      if (addImageButton == null) {
        break missingId;
      }

      id = R.id.alignCenterButton;
      ImageButton alignCenterButton = ViewBindings.findChildViewById(rootView, id);
      if (alignCenterButton == null) {
        break missingId;
      }

      id = R.id.alignLeftButton;
      ImageButton alignLeftButton = ViewBindings.findChildViewById(rootView, id);
      if (alignLeftButton == null) {
        break missingId;
      }

      id = R.id.alignRightButton;
      ImageButton alignRightButton = ViewBindings.findChildViewById(rootView, id);
      if (alignRightButton == null) {
        break missingId;
      }

      id = R.id.bottomButtonsLayout;
      LinearLayout bottomButtonsLayout = ViewBindings.findChildViewById(rootView, id);
      if (bottomButtonsLayout == null) {
        break missingId;
      }

      id = R.id.cardSizeIndicator;
      View cardSizeIndicator = ViewBindings.findChildViewById(rootView, id);
      if (cardSizeIndicator == null) {
        break missingId;
      }

      id = R.id.clearTextButton;
      ImageButton clearTextButton = ViewBindings.findChildViewById(rootView, id);
      if (clearTextButton == null) {
        break missingId;
      }

      id = R.id.copiesCountText;
      TextView copiesCountText = ViewBindings.findChildViewById(rootView, id);
      if (copiesCountText == null) {
        break missingId;
      }

      id = R.id.copiesDecreaseButton;
      ImageButton copiesDecreaseButton = ViewBindings.findChildViewById(rootView, id);
      if (copiesDecreaseButton == null) {
        break missingId;
      }

      id = R.id.copiesIncreaseButton;
      ImageButton copiesIncreaseButton = ViewBindings.findChildViewById(rootView, id);
      if (copiesIncreaseButton == null) {
        break missingId;
      }

      id = R.id.editText;
      EditText editText = ViewBindings.findChildViewById(rootView, id);
      if (editText == null) {
        break missingId;
      }

      id = R.id.editorScrollView;
      ScrollView editorScrollView = ViewBindings.findChildViewById(rootView, id);
      if (editorScrollView == null) {
        break missingId;
      }

      id = R.id.fontSizeDecreaseButton;
      ImageButton fontSizeDecreaseButton = ViewBindings.findChildViewById(rootView, id);
      if (fontSizeDecreaseButton == null) {
        break missingId;
      }

      id = R.id.fontSizeIncreaseButton;
      ImageButton fontSizeIncreaseButton = ViewBindings.findChildViewById(rootView, id);
      if (fontSizeIncreaseButton == null) {
        break missingId;
      }

      id = R.id.fontSizeText;
      TextView fontSizeText = ViewBindings.findChildViewById(rootView, id);
      if (fontSizeText == null) {
        break missingId;
      }

      id = R.id.formatControlsContent;
      LinearLayout formatControlsContent = ViewBindings.findChildViewById(rootView, id);
      if (formatControlsContent == null) {
        break missingId;
      }

      id = R.id.formatControlsExpandIcon;
      ImageView formatControlsExpandIcon = ViewBindings.findChildViewById(rootView, id);
      if (formatControlsExpandIcon == null) {
        break missingId;
      }

      id = R.id.formatControlsHeader;
      LinearLayout formatControlsHeader = ViewBindings.findChildViewById(rootView, id);
      if (formatControlsHeader == null) {
        break missingId;
      }

      id = R.id.formatControlsLayout;
      LinearLayout formatControlsLayout = ViewBindings.findChildViewById(rootView, id);
      if (formatControlsLayout == null) {
        break missingId;
      }

      id = R.id.imagePreview;
      ImageView imagePreview = ViewBindings.findChildViewById(rootView, id);
      if (imagePreview == null) {
        break missingId;
      }

      id = R.id.imagePreviewContainer;
      FrameLayout imagePreviewContainer = ViewBindings.findChildViewById(rootView, id);
      if (imagePreviewContainer == null) {
        break missingId;
      }

      id = R.id.lineSpacingDecreaseButton;
      ImageButton lineSpacingDecreaseButton = ViewBindings.findChildViewById(rootView, id);
      if (lineSpacingDecreaseButton == null) {
        break missingId;
      }

      id = R.id.lineSpacingIncreaseButton;
      ImageButton lineSpacingIncreaseButton = ViewBindings.findChildViewById(rootView, id);
      if (lineSpacingIncreaseButton == null) {
        break missingId;
      }

      id = R.id.lineSpacingText;
      TextView lineSpacingText = ViewBindings.findChildViewById(rootView, id);
      if (lineSpacingText == null) {
        break missingId;
      }

      id = R.id.previewImageView;
      ImageView previewImageView = ViewBindings.findChildViewById(rootView, id);
      if (previewImageView == null) {
        break missingId;
      }

      id = R.id.printButton;
      Button printButton = ViewBindings.findChildViewById(rootView, id);
      if (printButton == null) {
        break missingId;
      }

      id = R.id.removeImageButton;
      ImageButton removeImageButton = ViewBindings.findChildViewById(rootView, id);
      if (removeImageButton == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityMainBinding((ConstraintLayout) rootView, addImageButton, alignCenterButton,
          alignLeftButton, alignRightButton, bottomButtonsLayout, cardSizeIndicator,
          clearTextButton, copiesCountText, copiesDecreaseButton, copiesIncreaseButton, editText,
          editorScrollView, fontSizeDecreaseButton, fontSizeIncreaseButton, fontSizeText,
          formatControlsContent, formatControlsExpandIcon, formatControlsHeader,
          formatControlsLayout, imagePreview, imagePreviewContainer, lineSpacingDecreaseButton,
          lineSpacingIncreaseButton, lineSpacingText, previewImageView, printButton,
          removeImageButton, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
