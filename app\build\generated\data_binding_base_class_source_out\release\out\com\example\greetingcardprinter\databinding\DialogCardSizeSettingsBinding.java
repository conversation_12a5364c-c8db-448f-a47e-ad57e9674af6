// Generated by view binder compiler. Do not edit!
package com.example.greetingcardprinter.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.greetingcardprinter.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogCardSizeSettingsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView cardSizeInfoText;

  @NonNull
  public final Button closeButton;

  @NonNull
  public final TextView dialogTitle;

  private DialogCardSizeSettingsBinding(@NonNull LinearLayout rootView,
      @NonNull TextView cardSizeInfoText, @NonNull Button closeButton,
      @NonNull TextView dialogTitle) {
    this.rootView = rootView;
    this.cardSizeInfoText = cardSizeInfoText;
    this.closeButton = closeButton;
    this.dialogTitle = dialogTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogCardSizeSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogCardSizeSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_card_size_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogCardSizeSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cardSizeInfoText;
      TextView cardSizeInfoText = ViewBindings.findChildViewById(rootView, id);
      if (cardSizeInfoText == null) {
        break missingId;
      }

      id = R.id.closeButton;
      Button closeButton = ViewBindings.findChildViewById(rootView, id);
      if (closeButton == null) {
        break missingId;
      }

      id = R.id.dialogTitle;
      TextView dialogTitle = ViewBindings.findChildViewById(rootView, id);
      if (dialogTitle == null) {
        break missingId;
      }

      return new DialogCardSizeSettingsBinding((LinearLayout) rootView, cardSizeInfoText,
          closeButton, dialogTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
