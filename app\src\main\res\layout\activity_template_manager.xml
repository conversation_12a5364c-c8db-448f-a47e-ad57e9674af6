<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light"
    tools:context=".activities.TemplateManagerActivity">

    <!-- 工具栏 -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:title="模板管理"
        app:titleTextColor="@android:color/white"
        app:navigationIcon="?attr/homeAsUpIndicator" />

    <!-- 空状态提示 -->
    <LinearLayout
        android:id="@+id/emptyStateLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="32dp"
        android:visibility="gone">

        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:src="@drawable/ic_template_empty"
            android:alpha="0.3"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="暂无模板"
            android:textSize="18sp"
            android:textColor="@color/text_secondary"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="在主界面编辑内容后，点击保存模板按钮创建您的第一个模板"
            android:textSize="14sp"
            android:textColor="@color/text_hint"
            android:textAlignment="center"
            android:lineSpacingExtra="4dp" />

    </LinearLayout>

    <!-- 模板列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/templatesRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp"
        android:clipToPadding="false"
        tools:listitem="@layout/item_template" />

</LinearLayout>
