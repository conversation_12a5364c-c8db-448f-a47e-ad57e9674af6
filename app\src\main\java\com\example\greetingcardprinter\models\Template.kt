package com.example.greetingcardprinter.models

import java.util.*

/**
 * 模板数据类
 * 用于保存用户创建的文本模板，包含完整的格式信息
 */
data class Template(
    val id: String = UUID.randomUUID().toString(),
    val name: String,
    val content: String,  // 原始文本内容，保持所有格式
    val textLines: List<TextLine> = emptyList(),  // 解析后的文本行，包含对齐信息
    val fontSize: Float = 20f,  // 字体大小
    val lineSpacing: Float = 1.5f,  // 行间距
    val createdAt: Long = System.currentTimeMillis(),  // 创建时间戳
    val updatedAt: Long = System.currentTimeMillis()   // 更新时间戳
) {
    /**
     * 获取格式化的创建时间
     */
    fun getFormattedCreatedTime(): String {
        val date = Date(createdAt)
        val formatter = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
        return formatter.format(date)
    }
    
    /**
     * 获取格式化的更新时间
     */
    fun getFormattedUpdatedTime(): String {
        val date = Date(updatedAt)
        val formatter = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
        return formatter.format(date)
    }
    
    /**
     * 获取内容预览（前50个字符）
     */
    fun getContentPreview(): String {
        return if (content.length > 50) {
            content.take(50) + "..."
        } else {
            content
        }
    }
    
    /**
     * 创建更新后的模板副本
     */
    fun updateWith(
        name: String? = null,
        content: String? = null,
        textLines: List<TextLine>? = null,
        fontSize: Float? = null,
        lineSpacing: Float? = null
    ): Template {
        return this.copy(
            name = name ?: this.name,
            content = content ?: this.content,
            textLines = textLines ?: this.textLines,
            fontSize = fontSize ?: this.fontSize,
            lineSpacing = lineSpacing ?: this.lineSpacing,
            updatedAt = System.currentTimeMillis()
        )
    }
}
