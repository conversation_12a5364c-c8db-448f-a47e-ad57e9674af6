package com.example.greetingcardprinter.activities

import android.app.AlertDialog
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.greetingcardprinter.adapters.TemplateAdapter
import com.example.greetingcardprinter.databinding.ActivityTemplateManagerBinding
import com.example.greetingcardprinter.databinding.DialogTemplateEditBinding
import com.example.greetingcardprinter.models.Template
import com.example.greetingcardprinter.models.TextLine
import com.example.greetingcardprinter.models.TextAlignment
import com.example.greetingcardprinter.viewmodels.MainViewModel
import com.example.greetingcardprinter.viewmodels.TemplateOperationStatus
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * 模板管理Activity
 * 负责显示、编辑、删除模板
 */
class TemplateManagerActivity : AppCompatActivity() {

    private lateinit var binding: ActivityTemplateManagerBinding
    private val viewModel: MainViewModel by viewModels()
    private lateinit var templateAdapter: TemplateAdapter

    companion object {
        const val EXTRA_SELECTED_TEMPLATE = "selected_template"
        const val EXTRA_TEMPLATE_DATA = "template_data"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityTemplateManagerBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        setupRecyclerView()
        setupObservers()
        
        // 加载模板列表
        viewModel.loadTemplates()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        binding.toolbar.setNavigationOnClickListener {
            finish()
        }
    }

    private fun setupRecyclerView() {
        templateAdapter = TemplateAdapter(
            onTemplateClick = { template ->
                // 应用模板并返回主界面
                applyTemplateAndFinish(template)
            },
            onEditClick = { template ->
                showEditTemplateDialog(template)
            },
            onDeleteClick = { template ->
                showDeleteConfirmDialog(template)
            }
        )

        binding.templatesRecyclerView.apply {
            layoutManager = LinearLayoutManager(this@TemplateManagerActivity)
            adapter = templateAdapter
        }
    }

    private fun setupObservers() {
        // 观察模板列表变化
        lifecycleScope.launch {
            viewModel.templates.collectLatest { templates ->
                templateAdapter.submitList(templates)
                updateEmptyState(templates.isEmpty())
            }
        }

        // 观察模板操作状态
        lifecycleScope.launch {
            viewModel.templateOperationStatus.collectLatest { status ->
                when (status) {
                    is TemplateOperationStatus.Success -> {
                        Toast.makeText(this@TemplateManagerActivity, status.message, Toast.LENGTH_SHORT).show()
                        viewModel.resetTemplateOperationStatus()
                    }
                    is TemplateOperationStatus.Error -> {
                        Toast.makeText(this@TemplateManagerActivity, status.message, Toast.LENGTH_LONG).show()
                        viewModel.resetTemplateOperationStatus()
                    }
                    else -> {
                        // 其他状态不处理
                    }
                }
            }
        }
    }

    private fun updateEmptyState(isEmpty: Boolean) {
        if (isEmpty) {
            binding.emptyStateLayout.visibility = View.VISIBLE
            binding.templatesRecyclerView.visibility = View.GONE
        } else {
            binding.emptyStateLayout.visibility = View.GONE
            binding.templatesRecyclerView.visibility = View.VISIBLE
        }
    }

    private fun applyTemplateAndFinish(template: Template) {
        // 返回模板数据给主界面，让MainActivity来应用模板
        val resultIntent = Intent().apply {
            putExtra(EXTRA_SELECTED_TEMPLATE, template.id)
            // 传递模板的JSON数据
            putExtra(EXTRA_TEMPLATE_DATA, com.google.gson.Gson().toJson(template))
        }
        setResult(RESULT_OK, resultIntent)
        finish()
    }

    private fun showEditTemplateDialog(template: Template) {
        val dialogBinding = DialogTemplateEditBinding.inflate(layoutInflater)
        
        // 设置当前值
        dialogBinding.templateNameEditText.setText(template.name)
        dialogBinding.templateContentEditText.setText(template.content)

        val dialog = AlertDialog.Builder(this)
            .setView(dialogBinding.root)
            .create()

        dialogBinding.cancelButton.setOnClickListener {
            dialog.dismiss()
        }

        dialogBinding.saveButton.setOnClickListener {
            val newName = dialogBinding.templateNameEditText.text.toString().trim()
            val newContent = dialogBinding.templateContentEditText.text.toString()

            if (newName.isEmpty()) {
                Toast.makeText(this, "请输入模板名称", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            if (newContent.isEmpty()) {
                Toast.makeText(this, "请输入模板内容", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            // 检查名称是否重复（排除当前模板）
            if (viewModel.isTemplateNameExists(newName, template.id)) {
                Toast.makeText(this, "模板名称已存在", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            // 解析文本行（简单处理，每行默认左对齐）
            val textLines = newContent.split("\n").map { line ->
                TextLine(text = line, alignment = TextAlignment.LEFT)
            }

            // 更新模板
            val updatedTemplate = template.updateWith(
                name = newName,
                content = newContent,
                textLines = textLines
            )

            viewModel.updateTemplate(updatedTemplate)
            dialog.dismiss()
        }

        dialog.show()
    }

    private fun showDeleteConfirmDialog(template: Template) {
        AlertDialog.Builder(this)
            .setTitle("删除模板")
            .setMessage("确定要删除模板「${template.name}」吗？此操作无法撤销。")
            .setPositiveButton("删除") { _, _ ->
                viewModel.deleteTemplate(template.id)
            }
            .setNegativeButton("取消", null)
            .show()
    }
}
