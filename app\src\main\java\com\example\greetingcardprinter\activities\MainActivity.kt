package com.example.greetingcardprinter.activities

import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.greetingcardprinter.R
import com.example.greetingcardprinter.databinding.ActivityMainBinding
import com.example.greetingcardprinter.models.TextAlignment
import com.example.greetingcardprinter.models.Template
import com.example.greetingcardprinter.viewmodels.MainViewModel
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import android.view.View
import android.widget.AdapterView
import android.graphics.Color
import android.text.Editable
import android.text.TextWatcher
import com.example.greetingcardprinter.models.CardContent
import com.example.greetingcardprinter.viewmodels.PrintingStatus
import com.example.greetingcardprinter.models.CardSize
import android.util.Log
import com.example.greetingcardprinter.services.PrintService
import android.graphics.Bitmap
import android.graphics.pdf.PdfDocument
import android.os.ParcelFileDescriptor
import android.graphics.pdf.PdfRenderer
import com.example.greetingcardprinter.utils.mmToPx
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import android.view.ViewTreeObserver
import android.widget.LinearLayout
import com.example.greetingcardprinter.models.TextLine
import android.os.Handler
import android.os.Looper
import android.net.Uri
import android.widget.SeekBar
import android.content.Intent
import com.example.greetingcardprinter.utils.ImageUtils
import com.example.greetingcardprinter.models.ImagePosition
import android.app.AlertDialog
import com.example.greetingcardprinter.viewmodels.TemplateOperationStatus
import com.google.android.material.textfield.TextInputEditText

class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    val viewModel: MainViewModel by viewModels()
    private var isUpdatingText = false
    private var previewFile: File? = null
    private lateinit var printService: PrintService
    
    // 添加变量存储上次读取的剪贴板内容
    private var lastClipboardContent: String = ""
    // 添加变量控制是否应该自动读取剪贴板
    private var shouldAutoReadClipboard = true
    
    // 图片选择器启动器
    private val imagePickerLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            result.data?.let { data ->
                val imageUri = data.getStringExtra(ImagePickerActivity.EXTRA_IMAGE_URI)?.let { Uri.parse(it) }
                val position = data.getIntExtra(ImagePickerActivity.EXTRA_IMAGE_POSITION, 0)
                val widthPercentage = data.getIntExtra(ImagePickerActivity.EXTRA_IMAGE_WIDTH_PERCENTAGE, 70)
                val heightMm = data.getFloatExtra(ImagePickerActivity.EXTRA_IMAGE_HEIGHT_MM, 50f)
                
                // 计算实际宽度（毫米）
                val cardWidth = viewModel.selectedCardSize.value.width
                val imageWidth = cardWidth * widthPercentage / 100f
                
                if (imageUri != null) {
                    // 加载图片
                    val maxWidthPx = (cardWidth * 3).mmToPx(this).toInt() // 以毫米为单位的最大宽度
                    val maxHeightPx = (viewModel.selectedCardSize.value.height * 3).mmToPx(this).toInt()
                    
                    lifecycleScope.launch {
                        val bitmap = ImageUtils.loadBitmapFromUri(this@MainActivity, imageUri, maxWidthPx, maxHeightPx)
                        
                        // 设置图片到ViewModel
                        if (bitmap != null) {
                            val imagePosition = when (position) {
                                0 -> ImagePosition.TOP
                                1 -> ImagePosition.BOTTOM
                                2 -> ImagePosition.FULL
                                else -> ImagePosition.TOP
                            }
                            
                            viewModel.setCardImage(imageUri, bitmap, imagePosition, imageWidth, heightMm)
                            
                            // 更新UI显示图片
                            updateImagePreview()
                        } else {
                            Toast.makeText(this@MainActivity, "无法加载图片", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            }
        }
    }

    // 模板管理器启动器
    private val templateManagerLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            result.data?.let { data ->
                val selectedTemplateId = data.getStringExtra(TemplateManagerActivity.EXTRA_SELECTED_TEMPLATE)
                val templateDataJson = data.getStringExtra(TemplateManagerActivity.EXTRA_TEMPLATE_DATA)

                if (selectedTemplateId != null && templateDataJson != null) {
                    try {
                        // 解析模板数据并应用到当前ViewModel
                        val template = com.google.gson.Gson().fromJson(templateDataJson, Template::class.java)
                        viewModel.applyTemplate(template)
                        Toast.makeText(this, "模板已应用", Toast.LENGTH_SHORT).show()
                    } catch (e: Exception) {
                        Toast.makeText(this, "应用模板失败", Toast.LENGTH_SHORT).show()
                        Log.e("MainActivity", "解析模板数据失败", e)
                    }
                }
            }
        }
    }

    // 更新图片预览
    private fun updateImagePreview() {
        val imageContent = viewModel.getImageContent()
        if (imageContent != null && imageContent.bitmap != null) {
            binding.imagePreview.setImageBitmap(imageContent.bitmap)
            binding.imagePreviewContainer.visibility = View.VISIBLE
        } else {
            binding.imagePreviewContainer.visibility = View.GONE
        }
    }
    
    // 读取剪贴板内容
    private fun readClipboardContent() {
        Log.d("MainActivity", "开始读取剪贴板内容")
        val clipboard = getSystemService(CLIPBOARD_SERVICE) as android.content.ClipboardManager
        Log.d("MainActivity", "剪贴板服务获取状态: ${clipboard != null}")
        
        if (clipboard.hasPrimaryClip()) {
            Log.d("MainActivity", "剪贴板中有内容")
            val item = clipboard.primaryClip?.getItemAt(0)
            val text = item?.text?.toString() ?: ""
            Log.d("MainActivity", "读取到的剪贴板内容: $text")
            
            // 检查剪贴板内容是否与上次相同，并且不为空
            if (text.isNotEmpty() && text != lastClipboardContent) {
                Log.d("MainActivity", "发现新的剪贴板内容")
                // 更新上次读取的内容
                lastClipboardContent = text
                
                Log.d("MainActivity", "开始设置文本内容到编辑框")
                // 先清空输入框的现有内容
                binding.editText.text.clear()
                
                // 解析文本内容，识别抬头、正文和落款
                val parts = text.split("——").map { it.trim() }
                val formattedText = StringBuilder()
                
                when (parts.size) {
                    1 -> {
                        // 只有正文
                        val content = parts[0]
                        if (content.contains("：")) {
                            // 包含抬头
                            val (header, body) = content.split("：", limit = 2)
                            formattedText.append("${header}：\n")
                            // 正文部分不再自动添加缩进
                            formattedText.append(body)
                        } else {
                            // 纯正文，不添加缩进
                            formattedText.append(content)
                        }
                    }
                    2 -> {
                        // 有正文和落款
                        val content = parts[0]
                        val footer = parts[1]
                        
                        if (content.contains("：")) {
                            // 包含抬头
                            val (header, body) = content.split("：", limit = 2)
                            formattedText.append("${header}：\n")
                            // 正文部分不再自动添加缩进
                            formattedText.append("${body}\n")
                        } else {
                            // 只有正文，不添加缩进
                            formattedText.append("${content}\n")
                        }
                        formattedText.append("——${footer}")
                    }
                }
                
                // 设置新的文本内容
                binding.editText.setText(formattedText.toString())
                Log.d("MainActivity", "设置的文本内容: ${formattedText.toString()}")
                
                // 设置不同部分的对齐方式
                val lines = formattedText.toString().split("\n")
                var currentPosition = 0
                
                lines.forEachIndexed { index, line ->
                    val lineStart = currentPosition
                    val lineEnd = lineStart + line.length
                    
                    val alignment = when {
                        line.contains("：") -> {
                            Log.d("MainActivity", "检测到抬头行: $line，设置左对齐")
                            TextAlignment.LEFT  // 抬头左对齐
                        }
                        line.trim().startsWith("——") -> {
                            Log.d("MainActivity", "检测到落款行: $line，设置右对齐")
                            TextAlignment.RIGHT  // 落款右对齐
                        }
                        else -> {
                            Log.d("MainActivity", "检测到正文行: $line，设置左对齐")
                            TextAlignment.LEFT  // 正文左对齐
                        }
                    }
                    
                    // 确保落款行的对齐方式正确设置
                    if (line.trim().startsWith("——")) {
                        // 对于落款行，使用整行的范围
                        val fullLineStart = formattedText.toString().indexOf(line)
                        val fullLineEnd = fullLineStart + line.length
                        Log.d("MainActivity", "应用落款行对齐：从位置$fullLineStart 到$fullLineEnd，对齐方式：$alignment")
                        viewModel.setTextAlignment(alignment, fullLineStart, fullLineEnd)
                    } else {
                        Log.d("MainActivity", "应用文本对齐：行[$index]从位置$lineStart 到$lineEnd，对齐方式：$alignment")
                        viewModel.setTextAlignment(alignment, lineStart, lineEnd)
                    }
                    
                    currentPosition = lineEnd + 1  // +1 是为了包含换行符
                }
                
                // 更新卡片内容
                Log.d("MainActivity", "更新卡片内容")
                updateCardContent()
                Log.d("MainActivity", "剪贴板内容设置完成")
            } else {
                if (text.isEmpty()) {
                    Log.d("MainActivity", "剪贴板内容为空")
                } else {
                    Log.d("MainActivity", "剪贴板内容与上次相同，不处理")
                }
            }
        } else {
            Log.d("MainActivity", "剪贴板中没有可用内容")
        }
    }
    
    // 贺卡的实际尺寸（毫米）
    private val CARD_WIDTH_MM = 114f
    private val CARD_HEIGHT_MM = 85f
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // 初始化PrintService并设置到ViewModel
        printService = PrintService(this)
        viewModel.setPrintService(printService)

        // 初始化直接打印服务
        viewModel.initDirectPrintService(this)
        
        setupToolbar()
        setupEditorView()
        setupFormatControls()
        setupPrintControls()
        setupObservers()
        setupCardSizeIndicator()
        
        // 检查是否是首次启动应用，如果是则显示自动粘贴功能提示
        val prefs = getSharedPreferences("app_prefs", MODE_PRIVATE)
        if (!prefs.getBoolean("auto_paste_info_shown", false)) {
            // 显示自动粘贴功能提示对话框
            android.app.AlertDialog.Builder(this)
                .setTitle("自动粘贴功能")
                .setMessage("应用现在支持自动粘贴剪贴板内容！每次返回应用时，如果检测到新的剪贴板内容，将自动粘贴或提示您。\n\n您可以通过菜单中的选项随时开启或关闭此功能。")
                .setPositiveButton("了解") { _, _ ->
                    // 标记已显示过提示
                    prefs.edit().putBoolean("auto_paste_info_shown", true).apply()
                }
                .show()
        }
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
    }
    
    private fun setupCardSizeIndicator() {
        // 等待视图布局完成后再设置贺卡指示框的尺寸
        binding.cardSizeIndicator.viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                // 获取可用宽度
                val availableWidth = binding.cardSizeIndicator.width
                
                // 根据贺卡的宽高比例计算高度
                val aspectRatio = CARD_HEIGHT_MM / CARD_WIDTH_MM
                val calculatedHeight = (availableWidth * aspectRatio).toInt()
                
                // 设置贺卡指示框的高度
                val params = binding.cardSizeIndicator.layoutParams
                params.height = calculatedHeight
                binding.cardSizeIndicator.layoutParams = params
                
                // 移除监听器，避免重复调用
                binding.cardSizeIndicator.viewTreeObserver.removeOnGlobalLayoutListener(this)
            }
        })
    }
    
    private fun setupEditorView() {
        Log.d("MainActivity", "开始设置编辑器视图")
        // 设置1.5倍行间距
        binding.editText.setLineSpacing(0f, 1.5f)
        
        // 设置清除按钮的点击事件
        binding.clearTextButton.setOnClickListener {
            binding.editText.text.clear()
            updateCardContent()
            binding.clearTextButton.visibility = View.GONE
        }
        
        // 添加EditText长按事件监听器
        binding.editText.setOnLongClickListener { view ->
            try {
                Log.d("MainActivity", "EditText长按，尝试读取剪贴板内容")
                readClipboardContent()
                // 关闭软键盘
                val imm = getSystemService(INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
                imm.hideSoftInputFromWindow(view.windowToken, 0)
                true
            } catch (e: Exception) {
                Log.e("MainActivity", "读取剪贴板内容失败", e)
                Toast.makeText(this@MainActivity, "无法读取剪贴板内容，请确保已授予相关权限", Toast.LENGTH_SHORT).show()
                false
            }
        }

        // 添加文本变化监听器，实时更新内容
        binding.editText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
                // 检查输入长度是否会超过200个字符
                if ((s?.length ?: 0) + after - count > 200) {
                    Toast.makeText(this@MainActivity, "文字不能超过200个字", Toast.LENGTH_SHORT).show()
                }
            }
            
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                // 根据文本内容显示或隐藏清除按钮
                binding.clearTextButton.visibility = if (s?.isNotEmpty() == true) View.VISIBLE else View.GONE
            }
            
            override fun afterTextChanged(s: Editable?) {
                // 如果文字超过200个字，截取前200个字
                if ((s?.length ?: 0) > 200) {
                    isUpdatingText = true
                    s?.delete(200, s.length)
                    isUpdatingText = false
                }
                
                if (!isUpdatingText) {
                    updateCardContent()
                }
            }
        })
        
        // 设置编辑器相关的UI事件处理
        binding.editText.setOnEditorActionListener { _, actionId, event ->
            // 如果是回车键，让系统默认处理（插入换行符）
            if (event != null && event.keyCode == android.view.KeyEvent.KEYCODE_ENTER) {
                // 返回false让系统处理默认行为（插入换行符）
                return@setOnEditorActionListener false
            }
            // 其他情况更新内容
            updateCardContent()
            false
        }
        
        binding.editText.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                updateCardContent()
            }
        }
    }
    
    private fun setupFormatControls() {
        // 设置折叠功能
        var isExpanded = false
        binding.formatControlsHeader.setOnClickListener {
            isExpanded = !isExpanded
            binding.formatControlsContent.visibility = if (isExpanded) View.VISIBLE else View.GONE
            binding.formatControlsExpandIcon.rotation = if (isExpanded) 180f else 0f
        }

        // 文本对齐按钮
        binding.alignLeftButton.setOnClickListener {
            val selStart = binding.editText.selectionStart
            val selEnd = binding.editText.selectionEnd
            viewModel.setTextAlignment(TextAlignment.LEFT, selStart, selEnd)
        }
        
        binding.alignCenterButton.setOnClickListener {
            val selStart = binding.editText.selectionStart
            val selEnd = binding.editText.selectionEnd
            viewModel.setTextAlignment(TextAlignment.CENTER, selStart, selEnd)
        }
        
        binding.alignRightButton.setOnClickListener {
            val selStart = binding.editText.selectionStart
            val selEnd = binding.editText.selectionEnd
            viewModel.setTextAlignment(TextAlignment.RIGHT, selStart, selEnd)
        }
        
        // 字体大小控制
        binding.fontSizeIncreaseButton.setOnClickListener {
            viewModel.increaseFontSize()
        }
        
        binding.fontSizeDecreaseButton.setOnClickListener {
            viewModel.decreaseFontSize()
        }
        
        // 打印份数控制
        binding.copiesIncreaseButton.setOnClickListener {
            viewModel.increaseCopies()
        }
        
        binding.copiesDecreaseButton.setOnClickListener {
            viewModel.decreaseCopies()
        }

        // 行间距控制
        binding.lineSpacingIncreaseButton.setOnClickListener {
            viewModel.increaseLineSpacing()
        }
        
        binding.lineSpacingDecreaseButton.setOnClickListener {
            viewModel.decreaseLineSpacing()
        }

        // 添加图片按钮点击事件
        binding.addImageButton.setOnClickListener {
            val intent = Intent(this, ImagePickerActivity::class.java)
            imagePickerLauncher.launch(intent)
        }
        
        // 移除图片按钮点击事件
        binding.removeImageButton.setOnClickListener {
            viewModel.removeCardImage()
            binding.imagePreviewContainer.visibility = View.GONE
        }
    }
    
    private fun setupPrintControls() {
        binding.printButton.setOnClickListener {
            // 直接调用打印预览，不需要等待打印机连接
            viewModel.printCard()
        }

        // 模板功能已移至菜单中
    }
    
    private fun updateCardContent() {
        val text = binding.editText.text.toString()
        
        // 检查文本是否已经被设置了对齐方式
        if (binding.editText.text is android.text.Spannable) {
            val spannable = binding.editText.text as android.text.Spannable
            val lines = text.split("\n")
            val textLines = mutableListOf<TextLine>()
            
            // 检查每行的对齐方式
            var lineStart = 0
            lines.forEach { line ->
                val lineEnd = lineStart + line.length
                
                // 获取这一行应用的所有AlignmentSpan
                val alignmentSpans = spannable.getSpans(
                    lineStart, lineEnd, android.text.style.AlignmentSpan::class.java
                )
                
                val alignment = if (alignmentSpans.isNotEmpty()) {
                    // 使用应用于这一行的第一个AlignmentSpan
                    when (alignmentSpans[0].alignment) {
                        android.text.Layout.Alignment.ALIGN_NORMAL -> TextAlignment.LEFT
                        android.text.Layout.Alignment.ALIGN_CENTER -> TextAlignment.CENTER
                        android.text.Layout.Alignment.ALIGN_OPPOSITE -> TextAlignment.RIGHT
                        else -> TextAlignment.LEFT
                    }
                } else {
                    // 没有AlignmentSpan，使用默认的对齐方式
                    if (line.trim().startsWith("——")) {
                        TextAlignment.RIGHT  // 落款默认右对齐
                    } else {
                        TextAlignment.LEFT   // 其他内容默认左对齐
                    }
                }
                
                textLines.add(TextLine(line, alignment))
                lineStart = lineEnd + 1  // +1 是为了包含换行符
            }
            
            // 使用包含对齐信息的TextLine列表更新卡片内容
            viewModel.updateCardTextWithAlignment(textLines)
            
            Log.d("MainActivity", "使用带对齐信息的文本行更新卡片内容: ${textLines.map { "${it.text} (${it.alignment})" }}")
        } else {
            // 如果没有对齐信息，则使用简单的文本更新
        viewModel.updateCardText(text)
            Log.d("MainActivity", "使用普通文本更新卡片内容")
        }
    }
    
    private fun setupObservers() {
        lifecycleScope.launch {
            viewModel.cardContent.collectLatest { content ->
                // 更新编辑框的格式
                updateEditorFormat(content)
            }
        }
        
        // 观察打印状态并显示相应提示
        lifecycleScope.launch {
            viewModel.printingStatus.collectLatest { status ->
                when (status) {
                    is PrintingStatus.Success -> {
                        Toast.makeText(
                            this@MainActivity,
                            "打印已完成",
                            Toast.LENGTH_SHORT
                        ).show()
                        // 打印成功后禁用打印按钮3秒，防止重复打印
                        binding.printButton.isEnabled = false
                        Handler(Looper.getMainLooper()).postDelayed({
                            binding.printButton.isEnabled = true
                        }, 3000)
                    }
                    is PrintingStatus.Error -> {
                        Toast.makeText(
                            this@MainActivity,
                            "打印失败: ${status.message}",
                            Toast.LENGTH_LONG
                        ).show()
                    }
                    is PrintingStatus.Printing -> {
                        Toast.makeText(
                            this@MainActivity,
                            "正在打印...",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                    else -> {
                        // 其他状态不处理
                    }
                }
            }
        }
        
        // 观察字体大小变化并更新UI
        lifecycleScope.launch {
            viewModel.cardContent.collect { content ->
                // 更新字体大小显示
                binding.fontSizeText.text = content.fontSize.toInt().toString()
            }
        }

        // 观察模板操作状态
        lifecycleScope.launch {
            viewModel.templateOperationStatus.collectLatest { status ->
                when (status) {
                    is TemplateOperationStatus.Success -> {
                        Toast.makeText(this@MainActivity, status.message, Toast.LENGTH_SHORT).show()
                        viewModel.resetTemplateOperationStatus()
                    }
                    is TemplateOperationStatus.Error -> {
                        Toast.makeText(this@MainActivity, status.message, Toast.LENGTH_LONG).show()
                        viewModel.resetTemplateOperationStatus()
                    }
                    else -> {
                        // 其他状态不处理
                    }
                }
            }
        }
    }
    
    private fun updateEditorFormat(content: CardContent) {
        // 防止循环更新
        isUpdatingText = true
        
        // 保存当前光标位置
        val cursorPosition = binding.editText.selectionStart
        
        // 设置字体大小
        binding.editText.textSize = content.fontSize
        
        // 设置字体 - 从assets目录加载
        val typeface = android.graphics.Typeface.createFromAsset(assets, "fonts/qiantuxianmoti.ttf")
        binding.editText.typeface = typeface
        
        // 确保EditText本身不会覆盖我们设置的对齐方式
        binding.editText.gravity = android.view.Gravity.NO_GRAVITY
        
        // 使用SpannableStringBuilder来设置不同行的对齐方式
        val spannableText = android.text.SpannableStringBuilder()
        
        content.textLines.forEachIndexed { index, line ->
            // 获取当前行的起始位置
            val lineStart = spannableText.length
            
            // 添加当前行的文本
            spannableText.append(line.text)
            
            // 获取当前行的结束位置
            val lineEnd = spannableText.length
            
            // 设置对齐方式
            val alignment = when (line.alignment) {
                TextAlignment.LEFT -> android.text.Layout.Alignment.ALIGN_NORMAL
                TextAlignment.CENTER -> android.text.Layout.Alignment.ALIGN_CENTER
                TextAlignment.RIGHT -> android.text.Layout.Alignment.ALIGN_OPPOSITE
            }
            
            // 应用对齐方式，确保Span应用到整行，包括换行符
            spannableText.setSpan(
                android.text.style.AlignmentSpan.Standard(alignment),
                lineStart,
                lineEnd,  // 移除 +1，因为我们会在之后添加换行符
                android.text.Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            
            // 添加换行符（除了最后一行）
            if (index < content.textLines.size - 1) {
                spannableText.append("\n")
            }
        }
        
        // 设置文本
        binding.editText.setText(spannableText)
        
        // 恢复光标位置
        val finalPosition = if (cursorPosition > spannableText.length) {
            spannableText.length
        } else {
            cursorPosition
        }
        binding.editText.setSelection(finalPosition)
        
        // 更新打印份数显示
        binding.copiesCountText.text = "${content.copies}"
        
        // 更新行间距显示
        binding.lineSpacingText.text = String.format("%.1f", content.lineSpacing)
        // 更新EditText的行距
        binding.editText.setLineSpacing(0f, content.lineSpacing)
        
        isUpdatingText = false
    }
    
    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.main_menu, menu)
        // 添加自动粘贴功能的开关菜单项
        menu.add(Menu.NONE, MENU_TOGGLE_AUTO_PASTE, Menu.NONE, 
            if (shouldAutoReadClipboard) "关闭自动粘贴" else "开启自动粘贴")
        return true
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.menu_save_template -> {
                // 保存模板功能
                showSaveTemplateDialog()
                true
            }
            R.id.menu_manage_templates -> {
                // 模板管理功能
                val intent = Intent(this, TemplateManagerActivity::class.java)
                templateManagerLauncher.launch(intent)
                true
            }
            MENU_TOGGLE_AUTO_PASTE -> {
                // 切换自动粘贴功能的状态
                shouldAutoReadClipboard = !shouldAutoReadClipboard
                // 更新菜单项文本
                item.title = if (shouldAutoReadClipboard) "关闭自动粘贴" else "开启自动粘贴"
                // 显示提示
                Toast.makeText(
                    this,
                    if (shouldAutoReadClipboard) "自动粘贴功能已开启" else "自动粘贴功能已关闭",
                    Toast.LENGTH_SHORT
                ).show()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    companion object {
        private const val MENU_TOGGLE_AUTO_PASTE = 100
    }
    
    private fun generatePreviewPdf(content: CardContent, outputFile: File) {
        val document = PdfDocument()
        
        val pageInfo = PdfDocument.PageInfo.Builder(
            content.cardWidth.mmToPx(this).toInt(),
            content.cardHeight.mmToPx(this).toInt(),
            1
        ).create()

        val page = document.startPage(pageInfo)
        printService.drawCardOnCanvas(
            page.canvas,
            content,
            content.cardWidth.mmToPx(this),
            content.cardHeight.mmToPx(this)
        )
        document.finishPage(page)
        
        try {
            FileOutputStream(outputFile).use { out ->
                document.writeTo(out)
            }
        } catch (e: Exception) {
            Log.e("MainActivity", "Preview generation failed", e)
        } finally {
            document.close()
        }
    }

    private fun showPdfPreview(file: File) {
        try {
            val parcelFileDescriptor = ParcelFileDescriptor.open(file, ParcelFileDescriptor.MODE_READ_ONLY)
            val pdfRenderer = PdfRenderer(parcelFileDescriptor)
            
            val page = pdfRenderer.openPage(0)
            val bitmap = Bitmap.createBitmap(
                page.width, 
                page.height, 
                Bitmap.Config.ARGB_8888
            )
            
            page.render(bitmap, null, null, PdfRenderer.Page.RENDER_MODE_FOR_DISPLAY)
            page.close()
            
            // 显示bitmap到ImageView
            binding.previewImageView.apply {
                setImageBitmap(bitmap)
                visibility = View.VISIBLE
            }
            
            pdfRenderer.close()
            parcelFileDescriptor.close()
        } catch (e: Exception) {
            Log.e("MainActivity", "Failed to show PDF preview", e)
            Toast.makeText(this, "无法显示PDF预览", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onPause() {
        super.onPause()
        printService.cancelPrintJob()
    }

    override fun onResume() {
        super.onResume()
        // 添加延迟来确保应用完全获得焦点后再读取剪贴板
        if (shouldAutoReadClipboard) {
            // 使用Handler延迟执行，确保应用已完全获得焦点
            Handler(Looper.getMainLooper()).postDelayed({
                try {
                    // 获取剪贴板服务
                    val clipboard = getSystemService(CLIPBOARD_SERVICE) as android.content.ClipboardManager
                    
                    // 检查剪贴板是否有内容
                    if (clipboard.hasPrimaryClip()) {
                        val item = clipboard.primaryClip?.getItemAt(0)
                        val clipText = item?.text?.toString() ?: ""
                        
                        // 检查是否是新内容
                        if (clipText.isNotEmpty() && clipText != lastClipboardContent) {
                            // 检查编辑框是否为空或内容很少（少于5个字符）
                            val currentText = binding.editText.text.toString()
                            if (currentText.isEmpty() || currentText.length < 5) {
                                // 读取并应用剪贴板内容
                                readClipboardContent()
                                // 显示提示
                                Toast.makeText(this, "已自动粘贴剪贴板内容", Toast.LENGTH_SHORT).show()
                            } else {
                                // 编辑框有内容，显示更温和的提示
                                Toast.makeText(this, "检测到新的剪贴板内容，长按编辑框可粘贴", Toast.LENGTH_SHORT).show()
                            }
                        }
                    }
                } catch (e: Exception) {
                    Log.e("MainActivity", "自动读取剪贴板失败", e)
                }
            }, 500) // 延迟500毫秒，让应用完全获得焦点
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        printService.onDestroy()
        previewFile?.delete()  // 清理预览文件
    }

    override fun onBackPressed() {
        // 取消打印作业
        printService.cancelPrintJob()
        // 隐藏预览图层
        binding.previewImageView.visibility = View.GONE
        super.onBackPressed()
    }

    /**
     * 显示保存模板对话框
     */
    private fun showSaveTemplateDialog() {
        val currentText = binding.editText.text.toString().trim()
        if (currentText.isEmpty()) {
            Toast.makeText(this, "请先输入内容再保存模板", Toast.LENGTH_SHORT).show()
            return
        }

        // 创建输入对话框
        val editText = TextInputEditText(this).apply {
            hint = "请输入模板名称"
            setSingleLine()
        }

        val dialog = AlertDialog.Builder(this)
            .setTitle("保存模板")
            .setMessage("为当前内容创建一个模板")
            .setView(editText)
            .setPositiveButton("保存") { _, _ ->
                val templateName = editText.text.toString().trim()
                if (templateName.isEmpty()) {
                    Toast.makeText(this, "请输入模板名称", Toast.LENGTH_SHORT).show()
                    return@setPositiveButton
                }

                // 保存模板
                viewModel.saveCurrentContentAsTemplate(templateName)
            }
            .setNegativeButton("取消", null)
            .create()

        dialog.show()

        // 自动弹出键盘
        editText.requestFocus()
        val imm = getSystemService(INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
        imm.showSoftInput(editText, android.view.inputmethod.InputMethodManager.SHOW_IMPLICIT)
    }
}