// Generated by view binder compiler. Do not edit!
package com.example.greetingcardprinter.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.ScrollView;
import android.widget.SeekBar;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.greetingcardprinter.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityImagePickerBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button btnCancel;

  @NonNull
  public final Button btnConfirm;

  @NonNull
  public final Button btnSelectImage;

  @NonNull
  public final ImageView imagePreview;

  @NonNull
  public final SeekBar seekBarWidth;

  @NonNull
  public final Spinner spinnerPosition;

  @NonNull
  public final TextView tvHeightMm;

  @NonNull
  public final TextView tvImageSize;

  @NonNull
  public final TextView tvPositionDescription;

  @NonNull
  public final TextView tvWidthPercentage;

  private ActivityImagePickerBinding(@NonNull ScrollView rootView, @NonNull Button btnCancel,
      @NonNull Button btnConfirm, @NonNull Button btnSelectImage, @NonNull ImageView imagePreview,
      @NonNull SeekBar seekBarWidth, @NonNull Spinner spinnerPosition, @NonNull TextView tvHeightMm,
      @NonNull TextView tvImageSize, @NonNull TextView tvPositionDescription,
      @NonNull TextView tvWidthPercentage) {
    this.rootView = rootView;
    this.btnCancel = btnCancel;
    this.btnConfirm = btnConfirm;
    this.btnSelectImage = btnSelectImage;
    this.imagePreview = imagePreview;
    this.seekBarWidth = seekBarWidth;
    this.spinnerPosition = spinnerPosition;
    this.tvHeightMm = tvHeightMm;
    this.tvImageSize = tvImageSize;
    this.tvPositionDescription = tvPositionDescription;
    this.tvWidthPercentage = tvWidthPercentage;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityImagePickerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityImagePickerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_image_picker, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityImagePickerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnCancel;
      Button btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btnConfirm;
      Button btnConfirm = ViewBindings.findChildViewById(rootView, id);
      if (btnConfirm == null) {
        break missingId;
      }

      id = R.id.btnSelectImage;
      Button btnSelectImage = ViewBindings.findChildViewById(rootView, id);
      if (btnSelectImage == null) {
        break missingId;
      }

      id = R.id.imagePreview;
      ImageView imagePreview = ViewBindings.findChildViewById(rootView, id);
      if (imagePreview == null) {
        break missingId;
      }

      id = R.id.seekBarWidth;
      SeekBar seekBarWidth = ViewBindings.findChildViewById(rootView, id);
      if (seekBarWidth == null) {
        break missingId;
      }

      id = R.id.spinnerPosition;
      Spinner spinnerPosition = ViewBindings.findChildViewById(rootView, id);
      if (spinnerPosition == null) {
        break missingId;
      }

      id = R.id.tvHeightMm;
      TextView tvHeightMm = ViewBindings.findChildViewById(rootView, id);
      if (tvHeightMm == null) {
        break missingId;
      }

      id = R.id.tvImageSize;
      TextView tvImageSize = ViewBindings.findChildViewById(rootView, id);
      if (tvImageSize == null) {
        break missingId;
      }

      id = R.id.tvPositionDescription;
      TextView tvPositionDescription = ViewBindings.findChildViewById(rootView, id);
      if (tvPositionDescription == null) {
        break missingId;
      }

      id = R.id.tvWidthPercentage;
      TextView tvWidthPercentage = ViewBindings.findChildViewById(rootView, id);
      if (tvWidthPercentage == null) {
        break missingId;
      }

      return new ActivityImagePickerBinding((ScrollView) rootView, btnCancel, btnConfirm,
          btnSelectImage, imagePreview, seekBarWidth, spinnerPosition, tvHeightMm, tvImageSize,
          tvPositionDescription, tvWidthPercentage);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
