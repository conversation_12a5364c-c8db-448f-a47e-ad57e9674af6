package com.example.greetingcardprinter.activities

import android.app.Activity
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.view.MenuItem
import android.view.View
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.SeekBar
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import com.example.greetingcardprinter.R
import com.example.greetingcardprinter.databinding.ActivityImagePickerBinding
import com.example.greetingcardprinter.models.ImagePosition
import com.example.greetingcardprinter.utils.ImageUtils
import java.io.IOException

/**
 * 图片选择器Activity
 */
class ImagePickerActivity : AppCompatActivity() {

    private lateinit var binding: ActivityImagePickerBinding
    
    // 选择的图片Uri
    private var selectedImageUri: Uri? = null
    
    // 预览图片
    private var previewBitmap: Bitmap? = null
    
    // 图片在卡片上的位置
    private var imagePosition = ImagePosition.TOP
    
    // 图片宽度（百分比）
    private var widthPercentage = 70
    
    // 图片高度（毫米）
    private var heightMm = 50f
    
    // 图片选择器启动器
    private val imagePickerLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                selectedImageUri = uri
                loadAndShowPreview(uri)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityImagePickerBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // 设置标题和返回按钮
        supportActionBar?.apply {
            title = "选择图片"
            setDisplayHomeAsUpEnabled(true)
        }
        
        // 初始化位置选择器
        setupPositionSpinner()
        
        // 初始化宽度调整滑块
        setupWidthSeekBar()
        
        // 选择图片按钮
        binding.btnSelectImage.setOnClickListener {
            openImagePicker()
        }
        
        // 确认按钮
        binding.btnConfirm.setOnClickListener {
            if (selectedImageUri != null) {
                // 返回选中的图片信息
                val resultIntent = Intent().apply {
                    putExtra(EXTRA_IMAGE_URI, selectedImageUri.toString())
                    putExtra(EXTRA_IMAGE_POSITION, imagePosition.ordinal)
                    putExtra(EXTRA_IMAGE_WIDTH_PERCENTAGE, widthPercentage)
                    putExtra(EXTRA_IMAGE_HEIGHT_MM, heightMm)
                }
                setResult(Activity.RESULT_OK, resultIntent)
                finish()
            } else {
                Toast.makeText(this, "请先选择一张图片", Toast.LENGTH_SHORT).show()
            }
        }
        
        // 取消按钮
        binding.btnCancel.setOnClickListener {
            setResult(Activity.RESULT_CANCELED)
            finish()
        }
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            onBackPressed()
            return true
        }
        return super.onOptionsItemSelected(item)
    }
    
    private fun setupPositionSpinner() {
        val positions = arrayOf("图片在上方", "图片在下方", "仅显示图片")
        
        val adapter = ArrayAdapter(
            this,
            android.R.layout.simple_spinner_item,
            positions
        ).apply {
            setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        }
        
        binding.spinnerPosition.adapter = adapter
        
        binding.spinnerPosition.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                imagePosition = when(position) {
                    0 -> ImagePosition.TOP
                    1 -> ImagePosition.BOTTOM
                    2 -> ImagePosition.FULL
                    else -> ImagePosition.TOP
                }
                
                // 更新界面提示
                binding.tvPositionDescription.text = when(imagePosition) {
                    ImagePosition.TOP -> "图片将显示在文本上方"
                    ImagePosition.BOTTOM -> "图片将显示在文本下方"
                    ImagePosition.FULL -> "只显示图片，不显示文本"
                }
            }
            
            override fun onNothingSelected(parent: AdapterView<*>?) {
                // 不处理
            }
        }
    }
    
    private fun setupWidthSeekBar() {
        binding.seekBarWidth.progress = widthPercentage
        binding.tvWidthPercentage.text = "$widthPercentage%"
        
        binding.seekBarWidth.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                widthPercentage = progress
                binding.tvWidthPercentage.text = "$progress%"
                
                // 根据宽度百分比和原始图片比例计算高度
                updateHeightBasedOnWidth()
            }
            
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })
    }
    
    private fun updateHeightBasedOnWidth() {
        previewBitmap?.let { bitmap ->
            // 计算卡片宽度（默认114毫米）
            val cardWidthMm = 114f
            
            // 计算图片宽度（毫米）
            val imageWidthMm = cardWidthMm * widthPercentage / 100f
            
            // 根据原始图片比例计算高度
            val aspectRatio = bitmap.height.toFloat() / bitmap.width.toFloat()
            heightMm = imageWidthMm * aspectRatio
            
            // 更新高度显示
            binding.tvHeightMm.text = String.format("%.1f 毫米", heightMm)
        }
    }
    
    private fun openImagePicker() {
        val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
        imagePickerLauncher.launch(intent)
    }
    
    private fun loadAndShowPreview(uri: Uri) {
        try {
            // 设置图片预览
            binding.imagePreview.setImageURI(uri)
            binding.imagePreview.visibility = View.VISIBLE
            
            // 加载位图用于计算比例
            val bitmap = ImageUtils.loadBitmapFromUri(
                this,
                uri,
                1024, // 最大宽度，用于预览加载
                1024  // 最大高度，用于预览加载
            )
            
            if (bitmap != null) {
                previewBitmap = bitmap
                
                // 显示图片尺寸信息
                binding.tvImageSize.text = "原始尺寸: ${bitmap.width} x ${bitmap.height} 像素"
                binding.tvImageSize.visibility = View.VISIBLE
                
                // 更新高度
                updateHeightBasedOnWidth()
                
                // 显示确认按钮
                binding.btnConfirm.isEnabled = true
            } else {
                Toast.makeText(this, "无法加载图片", Toast.LENGTH_SHORT).show()
                binding.btnConfirm.isEnabled = false
            }
        } catch (e: IOException) {
            Toast.makeText(this, "加载图片时出错: ${e.message}", Toast.LENGTH_SHORT).show()
            binding.btnConfirm.isEnabled = false
        }
    }
    
    companion object {
        // 返回数据的键
        const val EXTRA_IMAGE_URI = "image_uri"
        const val EXTRA_IMAGE_POSITION = "image_position"
        const val EXTRA_IMAGE_WIDTH_PERCENTAGE = "image_width_percentage"
        const val EXTRA_IMAGE_HEIGHT_MM = "image_height_mm"
    }
} 