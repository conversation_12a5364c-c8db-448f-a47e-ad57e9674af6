package com.example.greetingcardprinter

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.pdf.PdfDocument
import android.os.Bundle
import android.os.CancellationSignal
import android.os.ParcelFileDescriptor
import android.print.PageRange
import android.print.PrintAttributes
import android.print.PrintDocumentAdapter
import android.print.PrintDocumentInfo
import android.print.PrintManager
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import com.example.greetingcardprinter.databinding.ActivityPrintSizeTestBinding

class PrintSizeTestActivity : AppCompatActivity() {
    private lateinit var binding: ActivityPrintSizeTestBinding
    
    companion object {
        private const val TAG = "PrintSizeTestActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPrintSizeTestBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 设置打印按钮点击事件
        binding.printButton.setOnClickListener {
            doPrint()
        }
    }

    private fun doPrint() {
        val printManager = getSystemService(Context.PRINT_SERVICE) as PrintManager
        val jobName = "CustomSizePrintTest_${System.currentTimeMillis()}"

        // 使用A4纸张尺寸（210mm x 297mm）
        val mediaSize = PrintAttributes.MediaSize.ISO_A4

        // 计算居中所需的边距
        val pageWidthMm = 210f  // A4宽度
        val pageHeightMm = 297f // A4高度
        val cardWidthMm = 114f  // 卡片宽度
        val cardHeightMm = 85f  // 卡片高度

        // 计算边距（毫米）
        val leftMarginMm = (pageWidthMm - cardWidthMm) / 2
        val topMarginMm = (pageHeightMm - cardHeightMm) / 2

        // 转换为千分之一英寸
        val mmToMils = 1000 / 25.4f
        val marginMils = (leftMarginMm * mmToMils).toInt()
        val topMarginMils = (topMarginMm * mmToMils).toInt()

        // 创建自定义边距
        val margins = PrintAttributes.Margins(
            marginMils,  // left
            topMarginMils,  // top
            marginMils,  // right
            topMarginMils   // bottom
        )

        // 创建打印属性
        val printAttributes = PrintAttributes.Builder()
            .setMediaSize(mediaSize)
            .setColorMode(PrintAttributes.COLOR_MODE_COLOR)
            .setResolution(PrintAttributes.Resolution("default", "默认", 300, 300))
            .setMinMargins(margins)  // 设置边距以居中内容
            .build()

        // 创建打印文档适配器
        val printAdapter = object : PrintDocumentAdapter() {
            private var currentAttributes: PrintAttributes? = null

            override fun onLayout(
                oldAttributes: PrintAttributes?,
                newAttributes: PrintAttributes,
                cancellationSignal: CancellationSignal,
                callback: LayoutResultCallback,
                extras: Bundle?
            ) {
                if (cancellationSignal.isCanceled) {
                    callback.onLayoutCancelled()
                    return
                }

                // 记录打印属性信息
                Log.d(TAG, "onLayout - 原始打印属性: " +
                    "mediaSize=${newAttributes.mediaSize?.id}(${newAttributes.mediaSize?.widthMils}x${newAttributes.mediaSize?.heightMils})")

                // 保存当前属性以供onWrite使用
                currentAttributes = newAttributes

                // 创建打印文档信息
                val info = PrintDocumentInfo.Builder(jobName)
                    .setContentType(PrintDocumentInfo.CONTENT_TYPE_DOCUMENT)
                    .setPageCount(1)
                    .build()

                // 总是返回true强制更新布局
                callback.onLayoutFinished(info, true)
            }

            override fun onWrite(
                pages: Array<out PageRange>,
                destination: ParcelFileDescriptor,
                cancellationSignal: CancellationSignal,
                callback: WriteResultCallback
            ) {
                if (cancellationSignal.isCanceled) {
                    callback.onWriteCancelled()
                    return
                }

                val pdfDocument = PdfDocument()

                try {
                    // 使用A4尺寸创建页面
                    val pageWidthPoints = (210f * 72f / 25.4f).toInt()  // A4宽度转换为点
                    val pageHeightPoints = (297f * 72f / 25.4f).toInt() // A4高度转换为点

                    val pageInfo = PdfDocument.PageInfo.Builder(pageWidthPoints, pageHeightPoints, 1).create()
                    val page = pdfDocument.startPage(pageInfo)

                    // 计算卡片在页面上的位置（水平居中，垂直位于下半部分）
                    val cardWidthPoints = (114f * 72f / 25.4f).toInt()
                    val cardHeightPoints = (85f * 72f / 25.4f).toInt()
                    
                    // 水平居中
                    val xOffset = (pageWidthPoints - cardWidthPoints) / 2f
                    
                    // 垂直位置：从页面底部向上180mm处开始（A4纸297mm - 180mm = 117mm）
                    val yOffset = ((297f - 180f) * 72f / 25.4f).toInt().toFloat()

                    Log.d(TAG, "onWrite - 卡片位置: xOffset=${xOffset}pt, yOffset=${yOffset}pt")
                    
                    // 保存画布状态
                    page.canvas.save()
                    
                    // 移动到卡片起始位置
                    page.canvas.translate(xOffset, yOffset)

                    // 绘制内容
                    drawContent(page.canvas, cardWidthPoints.toFloat(), cardHeightPoints.toFloat())

                    // 恢复画布状态
                    page.canvas.restore()

                    pdfDocument.finishPage(page)

                    // 写入PDF文件
                    pdfDocument.writeTo(ParcelFileDescriptor.AutoCloseOutputStream(destination))

                    // 完成写入
                    callback.onWriteFinished(arrayOf(PageRange.ALL_PAGES))

                } catch (e: Exception) {
                    Log.e(TAG, "Error writing PDF", e)
                    callback.onWriteFailed(e.message)
                } finally {
                    pdfDocument.close()
                }
            }

            private fun drawContent(canvas: Canvas, width: Float, height: Float) {
                // 绘制边框
                val paint = Paint().apply {
                    color = Color.BLACK
                    style = Paint.Style.STROKE
                    strokeWidth = 0.5f * 72f / 25.4f  // 0.5mm
                }
                canvas.drawRect(0f, 0f, width, height, paint)

                // 绘制文本
                paint.apply {
                    style = Paint.Style.FILL
                    textSize = 12f * 72f / 25.4f  // 12mm
                }

                // 绘制尺寸标注
                val text = String.format("%.1f × %.1f mm", 114f, 85f)
                val textWidth = paint.measureText(text)
                canvas.drawText(
                    text,
                    (width - textWidth) / 2,
                    height / 2,
                    paint
                )

                // 绘制中心十字线
                paint.apply {
                    strokeWidth = 0.25f * 72f / 25.4f  // 0.25mm
                    color = Color.GRAY
                }
                canvas.drawLine(0f, height/2, width, height/2, paint)  // 水平线
                canvas.drawLine(width/2, 0f, width/2, height, paint)   // 垂直线

                // 绘制裁切标记
                val markLength = 5f * 72f / 25.4f  // 5mm
                paint.color = Color.BLACK
                // 左上角
                canvas.drawLine(-markLength, 0f, 0f, 0f, paint)
                canvas.drawLine(0f, -markLength, 0f, 0f, paint)
                // 右上角
                canvas.drawLine(width, 0f, width + markLength, 0f, paint)
                canvas.drawLine(width, -markLength, width, 0f, paint)
                // 左下角
                canvas.drawLine(-markLength, height, 0f, height, paint)
                canvas.drawLine(0f, height, 0f, height + markLength, paint)
                // 右下角
                canvas.drawLine(width, height, width + markLength, height, paint)
                canvas.drawLine(width, height, width, height + markLength, paint)
            }
        }

        // 创建打印作业
        printManager.print(jobName, printAdapter, printAttributes)
    }
} 