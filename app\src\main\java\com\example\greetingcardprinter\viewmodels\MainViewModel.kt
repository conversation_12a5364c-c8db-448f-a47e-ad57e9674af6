package com.example.greetingcardprinter.viewmodels

import android.app.Application
import android.content.Context
import android.content.SharedPreferences
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.greetingcardprinter.models.CardContent
import com.example.greetingcardprinter.models.CardSize
import com.example.greetingcardprinter.models.ImageContent
import com.example.greetingcardprinter.models.ImagePosition
import com.example.greetingcardprinter.models.PrinterInfo
import com.example.greetingcardprinter.models.Template
import com.example.greetingcardprinter.models.TextAlignment
import com.example.greetingcardprinter.models.TextLine
import com.example.greetingcardprinter.services.DirectPrintService
import com.example.greetingcardprinter.services.MiuiEnhancedPrintService
import com.example.greetingcardprinter.services.PrinterDiscoveryService
import com.example.greetingcardprinter.services.PrintService
import com.example.greetingcardprinter.utils.TemplateManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.map

class MainViewModel(application: Application) : AndroidViewModel(application) {
    
    private val printerDiscoveryService = PrinterDiscoveryService(application)
    private var printService: PrintService? = null
    
    // 日志TAG
    companion object {
        private const val TAG = "MainViewModel"
        private const val KEY_FONT_SIZE = "font_size"
        private const val KEY_LINE_SPACING = "line_spacing"
    }
    
    // 添加SharedPreferences用于保存用户设置
    private val sharedPreferences: SharedPreferences = application.getSharedPreferences(
        "CardPrintPrefs", Context.MODE_PRIVATE
    )
    
    // 从SharedPreferences中获取保存的字号，默认为20px
    private val savedFontSize = sharedPreferences.getFloat(KEY_FONT_SIZE, 20f)
    
    // 初始化卡片内容，使用贺卡尺寸 (114mm x 85mm)
    // 从SharedPreferences中获取保存的行间距，默认为1.5
    private val savedLineSpacing = sharedPreferences.getFloat(KEY_LINE_SPACING, 1.5f)

    private val _cardContent = MutableStateFlow(CardContent(
        fontFamily = "千图纤墨体", 
        fontSize = savedFontSize,
        lineSpacing = savedLineSpacing,
        cardWidth = 114f,
        cardHeight = 85f
    ))
    val cardContent: StateFlow<CardContent> = _cardContent.asStateFlow()
    
    private val _selectedPrinter = MutableStateFlow<PrinterInfo?>(null)
    val selectedPrinter: StateFlow<PrinterInfo?> = _selectedPrinter.asStateFlow()
    
    private val _printingStatus = MutableStateFlow<PrintingStatus>(PrintingStatus.Idle)
    val printingStatus: StateFlow<PrintingStatus> = _printingStatus.asStateFlow()
    
    // 当前选择的贺卡尺寸，只有一种尺寸：贺卡
    private val _selectedCardSize = MutableStateFlow<CardSize>(CardSize.CARD)
    val selectedCardSize: StateFlow<CardSize> = _selectedCardSize.asStateFlow()
    
    // 直接使用printerDiscoveryService中的打印机列表
    val discoveredPrinters = printerDiscoveryService.printers
    
    // 添加打印事件流
    private val _printEvent = MutableStateFlow<PrintEvent?>(null)
    val printEvent: StateFlow<PrintEvent?> = _printEvent.asStateFlow()
    
    private var directPrintService: DirectPrintService? = null

    // 模板管理器
    private val templateManager = TemplateManager(application)

    // 模板相关状态
    private val _templates = MutableStateFlow<List<Template>>(emptyList())
    val templates: StateFlow<List<Template>> = _templates.asStateFlow()

    private val _templateOperationStatus = MutableStateFlow<TemplateOperationStatus>(TemplateOperationStatus.Idle)
    val templateOperationStatus: StateFlow<TemplateOperationStatus> = _templateOperationStatus.asStateFlow()

    init {
        // 启动打印机发现服务
        startDiscovery()
        // 加载模板列表
        loadTemplates()
    }
    
    fun startDiscovery() {
        printerDiscoveryService.startDiscovery()
    }
    
    fun stopDiscovery() {
        printerDiscoveryService.stopDiscovery()
    }
    
    fun selectPrinter(printer: PrinterInfo) {
        // 更新打印机连接状态
        val updatedPrinter = printer.copy(isConnected = true)
        _selectedPrinter.value = updatedPrinter
    }
    
    fun updateCardText(text: String) {
        val lines = text.split('\n').map { 
            TextLine(it, TextAlignment.LEFT) 
        }
        _cardContent.value = _cardContent.value.copy(textLines = lines)
    }
    
    // 添加新方法，支持更新带有对齐信息的文本
    fun updateCardTextWithAlignment(textLines: List<TextLine>) {
        _cardContent.value = _cardContent.value.copy(textLines = textLines)
        Log.d(TAG, "更新带对齐信息的文本: ${textLines.map { "${it.text} (${it.alignment})" }}")
    }
    
    fun setCurrentLineAlignment(alignment: TextAlignment) {
        // 找到当前光标所在行，并设置其对齐方式
        // 这里简化处理，实际应用中需要知道当前光标位置
        val lines = _cardContent.value.textLines.map { it.copy(alignment = alignment) }
        _cardContent.value = _cardContent.value.copy(textLines = lines)
    }
    
    // 设置文本对齐方式
    fun setTextAlignment(alignment: TextAlignment, selectionStart: Int, selectionEnd: Int) {
        val content = _cardContent.value
        val text = content.textLines.joinToString("\n") { it.text }
        
        // 获取选中的行
        val lines = text.split("\n")
        val startLine = text.substring(0, selectionStart).count { it == '\n' }
        val endLine = text.substring(0, selectionEnd).count { it == '\n' }
        
        Log.d("MainViewModel", "设置文本对齐：从第$startLine 行到第$endLine 行，对齐方式：$alignment")
        Log.d("MainViewModel", "选中文本范围：从位置$selectionStart 到$selectionEnd")
        Log.d("MainViewModel", "当前文本内容: \"$text\"")
        
        // 创建新的文本行列表
        val newTextLines = content.textLines.toMutableList()
        
        // 更新选中行的对齐方式
        for (i in startLine..endLine) {
            if (i < newTextLines.size) {
                val currentLine = newTextLines[i]
                Log.d("MainViewModel", "更新第${i}行的对齐方式：'${currentLine.text}' 从 ${currentLine.alignment} 到 $alignment")
                newTextLines[i] = currentLine.copy(alignment = alignment)
            }
        }
        
        // 更新内容
        _cardContent.value = content.copy(textLines = newTextLines)
        Log.d("MainViewModel", "文本对齐更新完成，共处理${endLine - startLine + 1}行")
        Log.d("MainViewModel", "更新后的文本行列表: ${newTextLines.map { "${it.text} (${it.alignment})" }}")
    }
    
    fun increaseFontSize() {
        val newSize = _cardContent.value.fontSize + 4f
        if (newSize <= 48f) { // 设置最大字号为48px
            _cardContent.value = _cardContent.value.copy(fontSize = newSize)
            saveFontSize(newSize)
        }
    }
    
    fun decreaseFontSize() {
        val newSize = (_cardContent.value.fontSize - 4f).coerceAtLeast(12f) // 设置最小字号为12px
        _cardContent.value = _cardContent.value.copy(fontSize = newSize)
        saveFontSize(newSize)
    }
    
    // 保存字号到SharedPreferences
    private fun saveFontSize(fontSize: Float) {
        sharedPreferences.edit().putFloat(KEY_FONT_SIZE, fontSize).apply()
    }
    
    fun increaseCopies() {
        val newCopies = _cardContent.value.copies + 1
        _cardContent.value = _cardContent.value.copy(copies = newCopies)
    }
    
    fun decreaseCopies() {
        val newCopies = (_cardContent.value.copies - 1).coerceAtLeast(1)
        _cardContent.value = _cardContent.value.copy(copies = newCopies)
    }
    
    fun setFontFamily(fontFamily: String) {
        _cardContent.value = _cardContent.value.copy(fontFamily = fontFamily)
    }
    
    fun setFontSize(fontSize: Float) {
        _cardContent.value = _cardContent.value.copy(fontSize = fontSize)
        saveFontSize(fontSize)
    }
    
    fun isConnectedToPrinter(): Boolean {
        return _selectedPrinter.value?.isConnected == true
    }
    
    // 设置贺卡尺寸 - 现在只有一种尺寸
    fun setCardSize(size: CardSize) {
        _selectedCardSize.value = size
        
        // 更新卡片内容的尺寸
        val currentContent = _cardContent.value
        _cardContent.value = currentContent.copy(
            cardWidth = size.width,
            cardHeight = size.height
        )
    }
    
    // 设置自定义贺卡尺寸 - 保留此方法以兼容现有代码，但实际上只会设置为固定尺寸
    fun setCustomCardSize(width: Float, height: Float) {
        // 忽略传入的尺寸，始终使用固定的贺卡尺寸
        setCardSize(CardSize.CARD)
    }
    
    // 添加方法让Activity提供PrintService
    fun setPrintService(service: PrintService) {
        printService = service
    }
    
    fun printCard() {
        Log.d(TAG, "printCard方法被调用")
        
        if (printService == null) {
            Log.e(TAG, "打印失败：PrintService未初始化")
            _printingStatus.value = PrintingStatus.Error("打印服务未初始化")
            return
        }
        
        // 更新打印状态
        _printingStatus.value = PrintingStatus.Printing
        
        viewModelScope.launch {
            try {
                val content = _cardContent.value
                // 获取当前选择的打印机或使用临时打印机
                var printer = _selectedPrinter.value
                
                // 如果打印机暂时丢失，尝试从缓存恢复
                if (printer == null || !printer.isConnected) {
                    val restored = printerDiscoveryService.restoreCachedPrinters()
                    if (restored) {
                        // 给系统一点时间处理恢复的打印机
                        kotlinx.coroutines.delay(500)
                        // 重新获取打印机状态
                        printer = _selectedPrinter.value
                    }
                }
                
                // 如果仍然没有打印机，使用临时打印机信息
                if (printer == null) {
                    printer = PrinterInfo(
                        id = "temp",
                        serviceName = "默认打印机",
                        description = "",
                        isConnected = true
                    )
                }
                
                // 检测是否为小米设备
                val isXiaomiDevice = isXiaomiDevice()
                Log.d(TAG, "当前是否为小米设备: $isXiaomiDevice")
                
                if (isXiaomiDevice) {
                    // 小米设备使用增强型打印服务
                    Log.d(TAG, "检测到小米设备，使用增强型打印服务")
                    
                    // 创建增强型打印服务
                    val context = getApplication<Application>() 
                    val enhancedPrintService = MiuiEnhancedPrintService(
                        printService!!,
                        context
                    )
                    
                    // 使用增强型打印服务
                    val result = enhancedPrintService.printWithVerification(content, printer)
                    
                    // 增强型打印服务自己会调用onPrintResult，不需要在这里更新状态
                    
                } else {
                    // 非小米设备使用标准打印服务
                    Log.d(TAG, "使用标准打印服务")
                    
                    // 发送打印任务
                    val printRequestAccepted = printService!!.printCard(content, printer)
                    
                    if (!printRequestAccepted) {
                        // 只有在打印请求失败时才立即显示错误
                        _printingStatus.value = PrintingStatus.Error("打印请求发送失败")
                    }
                    // 成功状态将由PrintService中的监控机制在真正打印成功后设置
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "打印过程中发生异常", e)
                _printingStatus.value = PrintingStatus.Error("打印异常: ${e.message}")
            }
        }
    }
    
    // 检测设备是否为小米设备
    private fun isXiaomiDevice(): Boolean {
        // 检查设备制造商
        val isXiaomiManufacturer = Build.MANUFACTURER.equals("Xiaomi", ignoreCase = true) || 
                                   Build.BRAND.equals("Xiaomi", ignoreCase = true) ||
                                   Build.BRAND.equals("Redmi", ignoreCase = true)
        
        // 如果制造商已确认是小米，直接返回
        if (isXiaomiManufacturer) {
            return true
        }
        
        // 尝试检测MIUI系统
        return isMiuiSystem()
    }
    
    // 检测是否为MIUI系统
    private fun isMiuiSystem(): Boolean {
        return try {
            val clazz = Class.forName("android.os.SystemProperties")
            val method = clazz.getDeclaredMethod("get", String::class.java)
            val prop = method.invoke(null, "ro.miui.ui.version.name") as String
            prop.isNotEmpty()
        } catch (e: Exception) {
            Log.e(TAG, "检测MIUI系统时出错", e)
            false
        }
    }
    
    // 打印完成后调用此方法 - 由PrintService在监控到真实打印状态后调用
    fun onPrintResult(success: Boolean, errorMessage: String = "") {
        Log.d(TAG, "收到打印结果: success=$success, message=$errorMessage")
        
        _printingStatus.value = if (success) {
            PrintingStatus.Success
        } else {
            PrintingStatus.Error(errorMessage)
        }
        // 清除打印事件
        _printEvent.value = null
    }
    
    fun initDirectPrintService(context: Context) {
        directPrintService = DirectPrintService(context)
    }
    
    /**
     * 直接打印贺卡，无需预览界面
     */
    fun printCardDirectly() {
        Log.d(TAG, "printCardDirectly方法被调用")
        
        if (directPrintService == null) {
            Log.e(TAG, "直接打印服务未初始化")
            _printingStatus.value = PrintingStatus.Error("直接打印服务未初始化")
            return
        }
        
        if (!isConnectedToPrinter()) {
            Log.e(TAG, "未连接到打印机")
            _printingStatus.value = PrintingStatus.Error("未连接到打印机")
            return
        }
        
        val printer = _selectedPrinter.value
        if (printer == null) {
            Log.e(TAG, "未选择打印机")
            _printingStatus.value = PrintingStatus.Error("未选择打印机")
            return
        }
        
        _printingStatus.value = PrintingStatus.Printing
        
        viewModelScope.launch {
            try {
                val content = _cardContent.value
                val success = directPrintService!!.printCardDirectly(content, printer)
                
                if (success) {
                    _printingStatus.value = PrintingStatus.Success
                } else {
                    _printingStatus.value = PrintingStatus.Error("直接打印失败")
                }
            } catch (e: Exception) {
                Log.e(TAG, "直接打印过程中发生异常", e)
                _printingStatus.value = PrintingStatus.Error("直接打印异常: ${e.message}")
            }
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        stopDiscovery()
    }
    
    fun increaseLineSpacing() {
        val newSpacing = (_cardContent.value.lineSpacing + 0.1f).coerceAtMost(2.0f)
        _cardContent.value = _cardContent.value.copy(lineSpacing = newSpacing)
        saveLineSpacing(newSpacing)
    }

    fun decreaseLineSpacing() {
        val newSpacing = (_cardContent.value.lineSpacing - 0.1f).coerceAtLeast(1.0f)
        _cardContent.value = _cardContent.value.copy(lineSpacing = newSpacing)
        saveLineSpacing(newSpacing)
    }

    private fun saveLineSpacing(spacing: Float) {
        sharedPreferences.edit().putFloat(KEY_LINE_SPACING, spacing).apply()
    }

    // 添加行间距调整方法
    fun updateLineSpacing(spacing: Float) {
        val clampedSpacing = spacing.coerceIn(1.0f, 3.0f)  // 限制行间距在1.0到3.0之间
        _cardContent.value = _cardContent.value.copy(lineSpacing = clampedSpacing)
        // 保存到SharedPreferences
        sharedPreferences.edit().putFloat(KEY_LINE_SPACING, clampedSpacing).apply()
    }
    
    // 设置打印份数
    fun setCopies(copies: Int) {
        if (copies > 0) {
            _cardContent.value = _cardContent.value.copy(copies = copies)
        }
    }
    
    fun getCopies(): Int {
        return _cardContent.value.copies
    }
    
    // 添加图片到卡片内容
    fun setCardImage(uri: Uri, bitmap: Bitmap?, position: ImagePosition, widthMm: Float, heightMm: Float) {
        val imageContent = ImageContent(
            uri = uri,
            bitmap = bitmap,
            position = position,
            widthMm = widthMm,
            heightMm = heightMm
        )
        _cardContent.value = _cardContent.value.copy(imageContent = imageContent)
        Log.d(TAG, "已添加图片：$uri，位置：$position，尺寸：${widthMm}x${heightMm}mm")
    }
    
    // 移除图片
    fun removeCardImage() {
        _cardContent.value = _cardContent.value.copy(imageContent = null)
        Log.d(TAG, "已移除图片")
    }
    
    // 检查是否有图片
    fun hasImage(): Boolean {
        return _cardContent.value.imageContent != null
    }
    
    // 获取当前图片内容
    fun getImageContent(): ImageContent? {
        return _cardContent.value.imageContent
    }

    // ==================== 模板相关方法 ====================

    /**
     * 加载所有模板
     */
    fun loadTemplates() {
        viewModelScope.launch {
            try {
                val templateList = templateManager.getAllTemplates()
                _templates.value = templateList
                Log.d(TAG, "加载模板列表成功，共${templateList.size}个模板")
            } catch (e: Exception) {
                Log.e(TAG, "加载模板列表失败", e)
                _templateOperationStatus.value = TemplateOperationStatus.Error("加载模板失败")
            }
        }
    }

    /**
     * 保存当前内容为模板
     */
    fun saveCurrentContentAsTemplate(templateName: String) {
        viewModelScope.launch {
            try {
                _templateOperationStatus.value = TemplateOperationStatus.Saving

                // 检查模板名称是否已存在
                if (templateManager.isTemplateNameExists(templateName)) {
                    _templateOperationStatus.value = TemplateOperationStatus.Error("模板名称已存在")
                    return@launch
                }

                val currentContent = _cardContent.value
                val template = templateManager.createTemplateFromContent(
                    name = templateName,
                    content = getCurrentTextContent(),
                    textLines = currentContent.textLines,
                    fontSize = currentContent.fontSize,
                    lineSpacing = currentContent.lineSpacing
                )

                val success = templateManager.saveTemplate(template)
                if (success) {
                    _templateOperationStatus.value = TemplateOperationStatus.Success("模板保存成功")
                    loadTemplates() // 重新加载模板列表
                } else {
                    _templateOperationStatus.value = TemplateOperationStatus.Error("保存模板失败")
                }
            } catch (e: Exception) {
                Log.e(TAG, "保存模板失败", e)
                _templateOperationStatus.value = TemplateOperationStatus.Error("保存模板失败: ${e.message}")
            }
        }
    }

    /**
     * 应用模板到当前编辑器
     */
    fun applyTemplate(template: Template) {
        viewModelScope.launch {
            try {
                _templateOperationStatus.value = TemplateOperationStatus.Loading

                // 更新卡片内容，包括文本行和格式设置
                _cardContent.value = _cardContent.value.copy(
                    textLines = template.textLines,
                    fontSize = template.fontSize,
                    lineSpacing = template.lineSpacing
                )

                // 保存字体设置到SharedPreferences
                sharedPreferences.edit().apply {
                    putFloat(KEY_FONT_SIZE, template.fontSize)
                    putFloat(KEY_LINE_SPACING, template.lineSpacing)
                    apply()
                }

                _templateOperationStatus.value = TemplateOperationStatus.Success("模板应用成功")
                Log.d(TAG, "应用模板成功: ${template.name}")
            } catch (e: Exception) {
                Log.e(TAG, "应用模板失败", e)
                _templateOperationStatus.value = TemplateOperationStatus.Error("应用模板失败")
            }
        }
    }

    /**
     * 删除模板
     */
    fun deleteTemplate(templateId: String) {
        viewModelScope.launch {
            try {
                _templateOperationStatus.value = TemplateOperationStatus.Deleting

                val success = templateManager.deleteTemplate(templateId)
                if (success) {
                    _templateOperationStatus.value = TemplateOperationStatus.Success("模板删除成功")
                    loadTemplates() // 重新加载模板列表
                } else {
                    _templateOperationStatus.value = TemplateOperationStatus.Error("删除模板失败")
                }
            } catch (e: Exception) {
                Log.e(TAG, "删除模板失败", e)
                _templateOperationStatus.value = TemplateOperationStatus.Error("删除模板失败")
            }
        }
    }

    /**
     * 更新模板
     */
    fun updateTemplate(template: Template) {
        viewModelScope.launch {
            try {
                _templateOperationStatus.value = TemplateOperationStatus.Saving

                val success = templateManager.updateTemplate(template)
                if (success) {
                    _templateOperationStatus.value = TemplateOperationStatus.Success("模板更新成功")
                    loadTemplates() // 重新加载模板列表
                } else {
                    _templateOperationStatus.value = TemplateOperationStatus.Error("更新模板失败")
                }
            } catch (e: Exception) {
                Log.e(TAG, "更新模板失败", e)
                _templateOperationStatus.value = TemplateOperationStatus.Error("更新模板失败")
            }
        }
    }

    /**
     * 检查模板名称是否已存在
     */
    fun isTemplateNameExists(name: String, excludeId: String? = null): Boolean {
        return templateManager.isTemplateNameExists(name, excludeId)
    }

    /**
     * 重置模板操作状态
     */
    fun resetTemplateOperationStatus() {
        _templateOperationStatus.value = TemplateOperationStatus.Idle
    }

    /**
     * 获取当前文本内容（用于保存模板）
     */
    private fun getCurrentTextContent(): String {
        return _cardContent.value.textLines.joinToString("\n") { it.text }
    }
}

sealed class PrintingStatus {
    object Idle : PrintingStatus()
    object Printing : PrintingStatus()
    object Success : PrintingStatus()
    data class Error(val message: String) : PrintingStatus()
}

// 打印事件数据类
data class PrintEvent(
    val content: CardContent,
    val printer: PrinterInfo
)

// 模板操作状态
sealed class TemplateOperationStatus {
    object Idle : TemplateOperationStatus()
    object Loading : TemplateOperationStatus()
    object Saving : TemplateOperationStatus()
    object Deleting : TemplateOperationStatus()
    data class Success(val message: String) : TemplateOperationStatus()
    data class Error(val message: String) : TemplateOperationStatus()
}