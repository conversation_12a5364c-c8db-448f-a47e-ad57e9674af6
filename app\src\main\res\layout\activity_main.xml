<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F7"
    tools:context=".activities.MainActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="#F5F5F7"
        android:elevation="0dp"
        android:theme="@style/ThemeOverlay.AppCompat.ActionBar"
        app:layout_constraintTop_toTopOf="parent"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
        app:title="@string/app_name"
        app:titleTextAppearance="@style/TextAppearance.AppCompat.Title"
        app:titleTextColor="#1D1D1F" />

    <LinearLayout
        android:id="@+id/formatControlsLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#FFFFFF"
        android:orientation="vertical"
        android:elevation="8dp"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <LinearLayout
            android:id="@+id/formatControlsHeader"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="16dp"
            android:clickable="true"
            android:focusable="true"
            android:background="?attr/selectableItemBackground">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="格式控制"
                android:textColor="#1D1D1F"
                android:textSize="16sp"
                android:fontFamily="sans-serif-medium"/>

            <ImageView
                android:id="@+id/formatControlsExpandIcon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_expand_more"
                app:tint="#1D1D1F"/>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/formatControlsContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp"
            android:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginBottom="16dp">

            <ImageButton
                android:id="@+id/alignLeftButton"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="48dp"
                android:background="@drawable/apple_circle_button_background"
                android:contentDescription="@string/align_left"
                android:src="@drawable/ic_align_left"
                android:padding="12dp"
                app:tint="#1D1D1F" />

            <ImageButton
                android:id="@+id/alignCenterButton"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="48dp"
                android:layout_marginHorizontal="8dp"
                android:background="@drawable/apple_circle_button_background"
                android:contentDescription="@string/align_center"
                android:src="@drawable/ic_align_center"
                android:padding="12dp"
                app:tint="#1D1D1F" />

            <ImageButton
                android:id="@+id/alignRightButton"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="48dp"
                android:background="@drawable/apple_circle_button_background"
                android:contentDescription="@string/align_right"
                android:src="@drawable/ic_align_right"
                android:padding="12dp"
                app:tint="#1D1D1F" />

            <Space
                android:layout_width="0dp"
                android:layout_weight="0.5"
                android:layout_height="wrap_content" />

            <ImageButton
                android:id="@+id/fontSizeDecreaseButton"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="48dp"
                android:background="@drawable/apple_circle_button_background"
                android:contentDescription="@string/decrease_font_size"
                android:src="@drawable/ic_text_decrease"
                android:padding="12dp"
                app:tint="#1D1D1F" />

            <TextView
                android:id="@+id/fontSizeText"
                android:layout_width="0dp"
                android:layout_weight="0.8"
                android:layout_height="wrap_content"
                android:text="18"
                android:textAlignment="center"
                android:textColor="#1D1D1F"
                android:textSize="17sp"
                android:fontFamily="sans-serif-medium"
                android:layout_marginHorizontal="8dp" />

            <ImageButton
                android:id="@+id/fontSizeIncreaseButton"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="48dp"
                android:background="@drawable/apple_circle_button_background"
                android:contentDescription="@string/increase_font_size"
                android:src="@drawable/ic_text_increase"
                android:padding="12dp"
                app:tint="#1D1D1F" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <ImageButton
                android:id="@+id/lineSpacingDecreaseButton"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="48dp"
                android:background="@drawable/apple_circle_button_background"
                android:contentDescription="@string/decrease_line_spacing"
                android:src="@drawable/ic_line_spacing_decrease"
                android:padding="12dp"
                app:tint="#1D1D1F" />

            <TextView
                android:id="@+id/lineSpacingText"
                android:layout_width="0dp"
                android:layout_weight="0.8"
                android:layout_height="wrap_content"
                android:text="1.5"
                android:textAlignment="center"
                android:textColor="#1D1D1F"
                android:textSize="17sp"
                android:fontFamily="sans-serif-medium"
                android:layout_marginHorizontal="8dp" />

            <ImageButton
                android:id="@+id/lineSpacingIncreaseButton"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="48dp"
                android:background="@drawable/apple_circle_button_background"
                android:contentDescription="@string/increase_line_spacing"
                android:src="@drawable/ic_line_spacing_increase"
                android:padding="12dp"
                app:tint="#1D1D1F" />

            <Space
                android:layout_width="0dp"
                android:layout_weight="0.5"
                android:layout_height="wrap_content" />

            <ImageButton
                android:id="@+id/copiesDecreaseButton"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="48dp"
                android:background="@drawable/apple_circle_button_background"
                android:contentDescription="@string/decrease_copies"
                android:src="@drawable/ic_copy_decrease"
                android:padding="12dp"
                app:tint="#1D1D1F" />

            <TextView
                android:id="@+id/copiesCountText"
                android:layout_width="0dp"
                android:layout_weight="0.8"
                android:layout_height="wrap_content"
                android:text="1"
                android:textAlignment="center"
                android:textColor="#1D1D1F"
                android:textSize="17sp"
                android:fontFamily="sans-serif-medium"
                android:layout_marginHorizontal="8dp" />

            <ImageButton
                android:id="@+id/copiesIncreaseButton"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="48dp"
                android:background="@drawable/apple_circle_button_background"
                android:contentDescription="@string/increase_copies"
                android:src="@drawable/ic_copy_increase"
                android:padding="12dp"
                app:tint="#1D1D1F" />
        </LinearLayout>
    </LinearLayout>
    </LinearLayout>

    <ScrollView
        android:id="@+id/editorScrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        android:background="#F5F5F7"
        android:clipToPadding="false"
        android:paddingHorizontal="16dp"
        app:layout_constraintBottom_toTopOf="@id/bottomButtonsLayout"
        app:layout_constraintTop_toBottomOf="@id/formatControlsLayout">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="4dp">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp">

                <Button
                    android:id="@+id/addImageButton"
                    android:layout_width="wrap_content"
                    android:layout_height="48dp"
                    android:layout_alignParentStart="true"
                    android:background="@drawable/apple_rounded_button_background"
                    android:text="添加图片"
                    android:textColor="#FFFFFF"
                    android:drawableStart="@android:drawable/ic_menu_gallery"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:drawablePadding="8dp"
                    android:contentDescription="添加图片" />

                <ImageButton
                    android:id="@+id/clearTextButton"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_alignParentEnd="true"
                    android:background="@drawable/apple_circle_button_background"
                    android:contentDescription="清空文本"
                    android:padding="12dp"
                    android:src="@drawable/ic_delete"
                    android:visibility="gone"
                    app:tint="#1D1D1F" />
            </RelativeLayout>

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="@drawable/apple_card_background"
                android:elevation="4dp">

                <View
                    android:id="@+id/cardSizeIndicator"
                    android:layout_width="match_parent"
                    android:layout_height="255dp"
                    android:layout_margin="16dp"
                    android:background="@drawable/card_size_indicator" />

                <EditText
                    android:id="@+id/editText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@android:color/transparent"
                    android:gravity="top"
                    android:importantForAutofill="no"
                    android:inputType="textMultiLine"
                    android:lineSpacingExtra="8px"
                    android:lineSpacingMultiplier="1.0"
                    android:minHeight="255dp"
                    android:padding="24dp"
                    android:textColor="#1D1D1F"
                    android:textSize="20px"
                    android:typeface="monospace"
                    android:contentDescription="@string/edit_text_description"
                    android:hint="@string/edit_text_hint"
                    android:elevation="3dp" />

                <FrameLayout
                    android:id="@+id/imagePreviewContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    android:elevation="3dp">
                    
                    <ImageView
                        android:id="@+id/imagePreview"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:adjustViewBounds="true"
                        android:scaleType="fitCenter"
                        android:layout_margin="24dp" />
                    
                    <ImageButton
                        android:id="@+id/removeImageButton"
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:layout_gravity="end|top"
                        android:layout_margin="8dp"
                        android:background="@drawable/apple_circle_button_background"
                        android:src="@android:drawable/ic_menu_close_clear_cancel"
                        android:contentDescription="删除图片"
                        android:padding="8dp" />
                </FrameLayout>

            </FrameLayout>

        </LinearLayout>
    </ScrollView>

    <LinearLayout
        android:id="@+id/bottomButtonsLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#FFFFFF"
        android:orientation="vertical"
        android:padding="24dp"
        android:elevation="8dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <!-- 打印按钮 -->
        <Button
            android:id="@+id/printButton"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@drawable/apple_button_background"
            android:text="@string/print_card"
            android:textColor="#FFFFFF"
            android:textSize="17sp"
            android:fontFamily="sans-serif-medium"
            android:elevation="2dp" />

    </LinearLayout>

    <ImageView
        android:id="@+id/previewImageView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:visibility="gone"
        android:scaleType="fitCenter"
        tools:src="@drawable/ic_launcher_background" />

</androidx.constraintlayout.widget.ConstraintLayout>