// Generated by view binder compiler. Do not edit!
package com.example.greetingcardprinter.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.greetingcardprinter.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemTemplateBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final ImageButton deleteButton;

  @NonNull
  public final ImageButton editButton;

  @NonNull
  public final TextView templateContentText;

  @NonNull
  public final TextView templateInfoText;

  @NonNull
  public final TextView templateNameText;

  @NonNull
  public final TextView templateTimeText;

  private ItemTemplateBinding(@NonNull CardView rootView, @NonNull ImageButton deleteButton,
      @NonNull ImageButton editButton, @NonNull TextView templateContentText,
      @NonNull TextView templateInfoText, @NonNull TextView templateNameText,
      @NonNull TextView templateTimeText) {
    this.rootView = rootView;
    this.deleteButton = deleteButton;
    this.editButton = editButton;
    this.templateContentText = templateContentText;
    this.templateInfoText = templateInfoText;
    this.templateNameText = templateNameText;
    this.templateTimeText = templateTimeText;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemTemplateBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemTemplateBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_template, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemTemplateBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.deleteButton;
      ImageButton deleteButton = ViewBindings.findChildViewById(rootView, id);
      if (deleteButton == null) {
        break missingId;
      }

      id = R.id.editButton;
      ImageButton editButton = ViewBindings.findChildViewById(rootView, id);
      if (editButton == null) {
        break missingId;
      }

      id = R.id.templateContentText;
      TextView templateContentText = ViewBindings.findChildViewById(rootView, id);
      if (templateContentText == null) {
        break missingId;
      }

      id = R.id.templateInfoText;
      TextView templateInfoText = ViewBindings.findChildViewById(rootView, id);
      if (templateInfoText == null) {
        break missingId;
      }

      id = R.id.templateNameText;
      TextView templateNameText = ViewBindings.findChildViewById(rootView, id);
      if (templateNameText == null) {
        break missingId;
      }

      id = R.id.templateTimeText;
      TextView templateTimeText = ViewBindings.findChildViewById(rootView, id);
      if (templateTimeText == null) {
        break missingId;
      }

      return new ItemTemplateBinding((CardView) rootView, deleteButton, editButton,
          templateContentText, templateInfoText, templateNameText, templateTimeText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
