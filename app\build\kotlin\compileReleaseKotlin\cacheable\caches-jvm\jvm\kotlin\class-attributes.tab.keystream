5com.example.greetingcardprinter.PrintSizeTestActivity?com.example.greetingcardprinter.PrintSizeTestActivity.CompanionIcom.example.greetingcardprinter.activities.CardSizeSettingsDialogFragmentScom.example.greetingcardprinter.activities.CardSizeSettingsDialogFragment.Companion>com.example.greetingcardprinter.activities.ImagePickerActivityHcom.example.greetingcardprinter.activities.ImagePickerActivity.Companion7com.example.greetingcardprinter.activities.MainActivityAcom.example.greetingcardprinter.activities.MainActivity.CompanionIcom.example.greetingcardprinter.activities.PrinterSelectionDialogFragmentBcom.example.greetingcardprinter.activities.TemplateManagerActivityLcom.example.greetingcardprinter.activities.TemplateManagerActivity.Companion7com.example.greetingcardprinter.adapters.PrinterAdapterIcom.example.greetingcardprinter.adapters.PrinterAdapter.PrinterViewHolderKcom.example.greetingcardprinter.adapters.PrinterAdapter.PrinterDiffCallback8com.example.greetingcardprinter.adapters.TemplateAdapterKcom.example.greetingcardprinter.adapters.TemplateAdapter.TemplateViewHolderMcom.example.greetingcardprinter.adapters.TemplateAdapter.TemplateDiffCallback2com.example.greetingcardprinter.models.CardContent/com.example.greetingcardprinter.models.TextLine3com.example.greetingcardprinter.models.ImageContent4com.example.greetingcardprinter.models.TextAlignment4com.example.greetingcardprinter.models.ImagePosition/com.example.greetingcardprinter.models.CardSize2com.example.greetingcardprinter.models.PrinterInfo/com.example.greetingcardprinter.models.Template;com.example.greetingcardprinter.services.DirectPrintServiceEcom.example.greetingcardprinter.services.DirectPrintService.CompanionAcom.example.greetingcardprinter.services.MiuiEnhancedPrintServiceKcom.example.greetingcardprinter.services.MiuiEnhancedPrintService.Companion5com.example.greetingcardprinter.services.PrintService?<EMAIL>?com.example.greetingcardprinter.utils.TemplateManager.Companion8com.example.greetingcardprinter.viewmodels.MainViewModelBcom.example.greetingcardprinter.viewmodels.MainViewModel.Companion9com.example.greetingcardprinter.viewmodels.PrintingStatus>com.example.greetingcardprinter.viewmodels.PrintingStatus.IdleBcom.example.greetingcardprinter.viewmodels.PrintingStatus.PrintingAcom.example.greetingcardprinter.viewmodels.PrintingStatus.Success?com.example.greetingcardprinter.viewmodels.PrintingStatus.Error5com.example.greetingcardprinter.viewmodels.PrintEventBcom.example.greetingcardprinter.viewmodels.TemplateOperationStatusGcom.example.greetingcardprinter.viewmodels.TemplateOperationStatus.IdleJcom.example.greetingcardprinter.viewmodels.TemplateOperationStatus.LoadingIcom.example.greetingcardprinter.viewmodels.TemplateOperationStatus.SavingKcom.example.greetingcardprinter.viewmodels.TemplateOperationStatus.DeletingJcom.example.greetingcardprinter.viewmodels.TemplateOperationStatus.SuccessHcom.example.greetingcardprinter.viewmodels.TemplateOperationStatus.Error?com.example.greetingcardprinter.databinding.ItemTemplateBindingJcom.example.greetingcardprinter.databinding.ActivityTemplateManagerBindingFcom.example.greetingcardprinter.databinding.ActivityImagePickerBinding?com.example.greetingcardprinter.databinding.ActivityMainBindingIcom.example.greetingcardprinter.databinding.DialogPrinterSelectionBindingHcom.example.greetingcardprinter.databinding.ActivityPrintSizeTestBindingEcom.example.greetingcardprinter.databinding.DialogTemplateEditBindingIcom.example.greetingcardprinter.databinding.DialogCardSizeSettingsBinding>com.example.greetingcardprinter.databinding.ItemPrinterBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             