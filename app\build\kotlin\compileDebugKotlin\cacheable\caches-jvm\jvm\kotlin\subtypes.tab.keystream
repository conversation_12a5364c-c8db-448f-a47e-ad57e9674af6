$androidx.fragment.app.DialogFragment(androidx.appcompat.app.AppCompatActivity(androidx.recyclerview.widget.ListAdapter4androidx.recyclerview.widget.RecyclerView.ViewHolder2androidx.recyclerview.widget.DiffUtil.ItemCallbackkotlin.Enum9com.example.greetingcardprinter.viewmodels.PrintingStatusBcom.example.greetingcardprinter.viewmodels.TemplateOperationStatus androidx.viewbinding.ViewBinding#androidx.lifecycle.AndroidViewModel                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   