1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.greetingcardprinter"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="29"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\CardPrint3\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\CardPrint3\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->D:\CardPrint3\app\src\main\AndroidManifest.xml:6:5-79
12-->D:\CardPrint3\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
13-->D:\CardPrint3\app\src\main\AndroidManifest.xml:7:5-76
13-->D:\CardPrint3\app\src\main\AndroidManifest.xml:7:22-73
14    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
14-->D:\CardPrint3\app\src\main\AndroidManifest.xml:8:5-76
14-->D:\CardPrint3\app\src\main\AndroidManifest.xml:8:22-73
15    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
15-->D:\CardPrint3\app\src\main\AndroidManifest.xml:9:5-79
15-->D:\CardPrint3\app\src\main\AndroidManifest.xml:9:22-76
16    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
16-->D:\CardPrint3\app\src\main\AndroidManifest.xml:10:5-81
16-->D:\CardPrint3\app\src\main\AndroidManifest.xml:10:22-78
17    <uses-permission android:name="android.permission.NEARBY_WIFI_DEVICES" />
17-->D:\CardPrint3\app\src\main\AndroidManifest.xml:11:5-78
17-->D:\CardPrint3\app\src\main\AndroidManifest.xml:11:22-75
18    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
18-->D:\CardPrint3\app\src\main\AndroidManifest.xml:12:5-80
18-->D:\CardPrint3\app\src\main\AndroidManifest.xml:12:22-77
19    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
19-->D:\CardPrint3\app\src\main\AndroidManifest.xml:13:5-81
19-->D:\CardPrint3\app\src\main\AndroidManifest.xml:13:22-78
20    <uses-permission android:name="android.permission.READ_CLIPBOARD" />
20-->D:\CardPrint3\app\src\main\AndroidManifest.xml:14:5-73
20-->D:\CardPrint3\app\src\main\AndroidManifest.xml:14:22-70
21
22    <!-- 网络服务发现权限 -->
23    <uses-permission android:name="android.permission.NFC" />
23-->D:\CardPrint3\app\src\main\AndroidManifest.xml:17:5-62
23-->D:\CardPrint3\app\src\main\AndroidManifest.xml:17:22-59
24    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
24-->D:\CardPrint3\app\src\main\AndroidManifest.xml:18:5-79
24-->D:\CardPrint3\app\src\main\AndroidManifest.xml:18:22-76
25
26    <!-- Android 13+ 需要的权限 -->
27    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
27-->D:\CardPrint3\app\src\main\AndroidManifest.xml:21:5-77
27-->D:\CardPrint3\app\src\main\AndroidManifest.xml:21:22-74
28
29    <permission
29-->[androidx.core:core:1.12.0] D:\CardPrint2\caches\8.10.2\transforms\9653369f29d51ad73d9709a09e371dee\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
30        android:name="com.example.greetingcardprinter.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
30-->[androidx.core:core:1.12.0] D:\CardPrint2\caches\8.10.2\transforms\9653369f29d51ad73d9709a09e371dee\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
31        android:protectionLevel="signature" />
31-->[androidx.core:core:1.12.0] D:\CardPrint2\caches\8.10.2\transforms\9653369f29d51ad73d9709a09e371dee\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
32
33    <uses-permission android:name="com.example.greetingcardprinter.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
33-->[androidx.core:core:1.12.0] D:\CardPrint2\caches\8.10.2\transforms\9653369f29d51ad73d9709a09e371dee\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
33-->[androidx.core:core:1.12.0] D:\CardPrint2\caches\8.10.2\transforms\9653369f29d51ad73d9709a09e371dee\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
34
35    <application
35-->D:\CardPrint3\app\src\main\AndroidManifest.xml:23:5-67:19
36        android:allowBackup="true"
36-->D:\CardPrint3\app\src\main\AndroidManifest.xml:24:9-35
37        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
37-->[androidx.core:core:1.12.0] D:\CardPrint2\caches\8.10.2\transforms\9653369f29d51ad73d9709a09e371dee\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
38        android:dataExtractionRules="@xml/data_extraction_rules"
38-->D:\CardPrint3\app\src\main\AndroidManifest.xml:25:9-65
39        android:debuggable="true"
40        android:extractNativeLibs="false"
41        android:fullBackupContent="@xml/backup_rules"
41-->D:\CardPrint3\app\src\main\AndroidManifest.xml:26:9-54
42        android:icon="@mipmap/ic_launcher"
42-->D:\CardPrint3\app\src\main\AndroidManifest.xml:27:9-43
43        android:label="@string/app_name"
43-->D:\CardPrint3\app\src\main\AndroidManifest.xml:28:9-41
44        android:roundIcon="@mipmap/ic_launcher_round"
44-->D:\CardPrint3\app\src\main\AndroidManifest.xml:29:9-54
45        android:supportsRtl="true"
45-->D:\CardPrint3\app\src\main\AndroidManifest.xml:30:9-35
46        android:testOnly="true"
47        android:theme="@style/Theme.CardPrint" >
47-->D:\CardPrint3\app\src\main\AndroidManifest.xml:31:9-47
48        <activity
48-->D:\CardPrint3\app\src\main\AndroidManifest.xml:34:9-41:20
49            android:name="com.example.greetingcardprinter.activities.MainActivity"
49-->D:\CardPrint3\app\src\main\AndroidManifest.xml:35:13-52
50            android:exported="true" >
50-->D:\CardPrint3\app\src\main\AndroidManifest.xml:36:13-36
51            <intent-filter>
51-->D:\CardPrint3\app\src\main\AndroidManifest.xml:37:13-40:29
52                <action android:name="android.intent.action.MAIN" />
52-->D:\CardPrint3\app\src\main\AndroidManifest.xml:38:17-69
52-->D:\CardPrint3\app\src\main\AndroidManifest.xml:38:25-66
53
54                <category android:name="android.intent.category.LAUNCHER" />
54-->D:\CardPrint3\app\src\main\AndroidManifest.xml:39:17-77
54-->D:\CardPrint3\app\src\main\AndroidManifest.xml:39:27-74
55            </intent-filter>
56        </activity>
57        <activity
57-->D:\CardPrint3\app\src\main\AndroidManifest.xml:43:9-46:39
58            android:name="com.example.greetingcardprinter.PrintSizeTestActivity"
58-->D:\CardPrint3\app\src\main\AndroidManifest.xml:44:13-50
59            android:exported="true"
59-->D:\CardPrint3\app\src\main\AndroidManifest.xml:46:13-36
60            android:label="打印尺寸测试" />
60-->D:\CardPrint3\app\src\main\AndroidManifest.xml:45:13-35
61        <activity
61-->D:\CardPrint3\app\src\main\AndroidManifest.xml:48:9-51:40
62            android:name="com.example.greetingcardprinter.activities.ImagePickerActivity"
62-->D:\CardPrint3\app\src\main\AndroidManifest.xml:49:13-59
63            android:exported="false"
63-->D:\CardPrint3\app\src\main\AndroidManifest.xml:51:13-37
64            android:label="选择图片" />
64-->D:\CardPrint3\app\src\main\AndroidManifest.xml:50:13-33
65        <activity
65-->D:\CardPrint3\app\src\main\AndroidManifest.xml:53:9-56:40
66            android:name="com.example.greetingcardprinter.activities.TemplateManagerActivity"
66-->D:\CardPrint3\app\src\main\AndroidManifest.xml:54:13-63
67            android:exported="false"
67-->D:\CardPrint3\app\src\main\AndroidManifest.xml:56:13-37
68            android:label="模板管理" />
68-->D:\CardPrint3\app\src\main\AndroidManifest.xml:55:13-33
69
70        <provider
71            android:name="androidx.core.content.FileProvider"
71-->D:\CardPrint3\app\src\main\AndroidManifest.xml:59:13-62
72            android:authorities="com.example.greetingcardprinter.fileprovider"
72-->D:\CardPrint3\app\src\main\AndroidManifest.xml:60:13-64
73            android:exported="false"
73-->D:\CardPrint3\app\src\main\AndroidManifest.xml:61:13-37
74            android:grantUriPermissions="true" >
74-->D:\CardPrint3\app\src\main\AndroidManifest.xml:62:13-47
75            <meta-data
75-->D:\CardPrint3\app\src\main\AndroidManifest.xml:63:13-65:54
76                android:name="android.support.FILE_PROVIDER_PATHS"
76-->D:\CardPrint3\app\src\main\AndroidManifest.xml:64:17-67
77                android:resource="@xml/file_paths" />
77-->D:\CardPrint3\app\src\main\AndroidManifest.xml:65:17-51
78        </provider>
79        <provider
79-->[androidx.emoji2:emoji2:1.2.0] D:\CardPrint2\caches\8.10.2\transforms\99a5bec0319d01b025141ac7c6c65239\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
80            android:name="androidx.startup.InitializationProvider"
80-->[androidx.emoji2:emoji2:1.2.0] D:\CardPrint2\caches\8.10.2\transforms\99a5bec0319d01b025141ac7c6c65239\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
81            android:authorities="com.example.greetingcardprinter.androidx-startup"
81-->[androidx.emoji2:emoji2:1.2.0] D:\CardPrint2\caches\8.10.2\transforms\99a5bec0319d01b025141ac7c6c65239\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
82            android:exported="false" >
82-->[androidx.emoji2:emoji2:1.2.0] D:\CardPrint2\caches\8.10.2\transforms\99a5bec0319d01b025141ac7c6c65239\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
83            <meta-data
83-->[androidx.emoji2:emoji2:1.2.0] D:\CardPrint2\caches\8.10.2\transforms\99a5bec0319d01b025141ac7c6c65239\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
84                android:name="androidx.emoji2.text.EmojiCompatInitializer"
84-->[androidx.emoji2:emoji2:1.2.0] D:\CardPrint2\caches\8.10.2\transforms\99a5bec0319d01b025141ac7c6c65239\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
85                android:value="androidx.startup" />
85-->[androidx.emoji2:emoji2:1.2.0] D:\CardPrint2\caches\8.10.2\transforms\99a5bec0319d01b025141ac7c6c65239\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
86            <meta-data
86-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\CardPrint2\caches\8.10.2\transforms\b0c8f465de314c122842236e7cbdaa66\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
87                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
87-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\CardPrint2\caches\8.10.2\transforms\b0c8f465de314c122842236e7cbdaa66\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
88                android:value="androidx.startup" />
88-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\CardPrint2\caches\8.10.2\transforms\b0c8f465de314c122842236e7cbdaa66\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
89            <meta-data
89-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\CardPrint2\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
90                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
90-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\CardPrint2\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
91                android:value="androidx.startup" />
91-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\CardPrint2\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
92        </provider>
93
94        <receiver
94-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\CardPrint2\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
95            android:name="androidx.profileinstaller.ProfileInstallReceiver"
95-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\CardPrint2\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
96            android:directBootAware="false"
96-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\CardPrint2\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
97            android:enabled="true"
97-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\CardPrint2\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
98            android:exported="true"
98-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\CardPrint2\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
99            android:permission="android.permission.DUMP" >
99-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\CardPrint2\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
100            <intent-filter>
100-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\CardPrint2\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
101                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
101-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\CardPrint2\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
101-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\CardPrint2\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
102            </intent-filter>
103            <intent-filter>
103-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\CardPrint2\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
104                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
104-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\CardPrint2\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
104-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\CardPrint2\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
105            </intent-filter>
106            <intent-filter>
106-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\CardPrint2\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
107                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
107-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\CardPrint2\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
107-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\CardPrint2\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
108            </intent-filter>
109            <intent-filter>
109-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\CardPrint2\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
110                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
110-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\CardPrint2\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
110-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\CardPrint2\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
111            </intent-filter>
112        </receiver>
113    </application>
114
115</manifest>
