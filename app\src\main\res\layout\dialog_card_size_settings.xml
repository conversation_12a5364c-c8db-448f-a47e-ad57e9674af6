<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:id="@+id/dialogTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="贺卡尺寸信息"
        android:textAlignment="center"
        android:textSize="18sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/cardSizeInfoText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="贺卡尺寸: 114mm × 85mm"
        android:textAlignment="center"
        android:textSize="16sp" />

    <Button
        android:id="@+id/closeButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="关闭" />

</LinearLayout> 