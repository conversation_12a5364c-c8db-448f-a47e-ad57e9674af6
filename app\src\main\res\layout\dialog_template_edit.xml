<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="编辑模板"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:layout_marginBottom="16dp" />

    <!-- 模板名称 -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="模板名称">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/templateNameEditText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:inputType="text" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- 模板内容 -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="模板内容">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/templateContentEditText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minLines="4"
            android:maxLines="8"
            android:inputType="textMultiLine"
            android:gravity="top"
            android:scrollbars="vertical" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- 按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <Button
            android:id="@+id/cancelButton"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="取消"
            android:textSize="14sp"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/saveButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="保存"
            android:textSize="14sp" />

    </LinearLayout>

</LinearLayout>
