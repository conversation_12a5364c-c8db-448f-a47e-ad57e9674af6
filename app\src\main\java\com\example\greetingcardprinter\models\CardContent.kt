package com.example.greetingcardprinter.models

import android.graphics.Bitmap
import android.net.Uri

data class CardContent(
    val textLines: List<TextLine> = emptyList(),
    val fontSize: Float = 14f,
    val fontFamily: String = "sans-serif",
    val copies: Int = 1,
    val cardWidth: Float = 114f,  // 默认宽度，单位mm
    val cardHeight: Float = 85f,   // 默认高度，单位mm
    val lineSpacing: Float = 1.5f, // 行间距属性，默认值1.5f
    val imageContent: ImageContent? = null // 新增：图片内容
)

data class TextLine(
    val text: String,
    val alignment: TextAlignment = TextAlignment.LEFT
)

// 新增：图片内容数据类
data class ImageContent(
    val uri: Uri,                         // 图片文件URI
    val bitmap: Bitmap? = null,           // 图片位图对象，可以是null，用于缓存
    val position: ImagePosition = ImagePosition.TOP, // 图片位置
    val widthMm: Float = 80f,             // 图片宽度（毫米）
    val heightMm: Float = 60f,            // 图片高度（毫米）
    val marginTopMm: Float = 4f,          // 上边距（毫米）
    val marginBottomMm: Float = 4f        // 下边距（毫米）
)

enum class TextAlignment {
    LEFT, CENTER, RIGHT
}

// 新增：图片位置枚举
enum class ImagePosition {
    TOP,      // 图片在文本上方
    BOTTOM,   // 图片在文本下方
    FULL      // 图片填满整个卡片（不显示文本）
}

// 预定义的贺卡尺寸
enum class CardSize(val width: Float, val height: Float, val displayName: String) {
    CARD(114f, 85f, "贺卡 (114×85mm)")
}