package com.example.greetingcardprinter.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.text.TextPaint

object EmojiUtils {
    // 扩展Emoji Unicode范围以支持更多Emoji
    private val EMOJI_RANGES = arrayOf(
        IntRange(0x1F300, 0x1F9FF),  // 杂项符号和象形文字
        IntRange(0x2600, 0x26FF),    // 杂项符号
        IntRange(0x2700, 0x27BF),    // 装饰符号
        IntRange(0xFE00, 0xFE0F),    // 变体选择器
        IntRange(0x1F1E6, 0x1F1FF),  // 国旗
        IntRange(0x1F000, 0x1F02F),  // 麻将牌
        IntRange(0x1F0A0, 0x1F0FF),  // 扑克牌
        IntRange(0x1F100, 0x1F1FF),  // 带圈字符和国家指示符
        IntRange(0x1F200, 0x1F2FF),  // 带圈表意文字补充
        IntRange(0x1F900, 0x1F9FF),  // 补充符号和象形文字
        IntRange(0x1FA70, 0x1FAFF)   // 符号和象形文字扩展-A
    )

    // 检查字符是否是emoji
    fun isEmoji(char: Char): Boolean {
        val codePoint = char.code
        return EMOJI_RANGES.any { range -> codePoint in range }
    }

    // 将emoji转换为位图（保留但简化）
    fun convertEmojiToBitmap(emoji: String, textPaint: TextPaint, size: Float): Bitmap {
        val bitmap = Bitmap.createBitmap(size.toInt(), size.toInt(), Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        
        // 计算emoji的边界
        val bounds = Rect()
        textPaint.getTextBounds(emoji, 0, emoji.length, bounds)
        
        // 计算绘制位置，使emoji居中
        val x = (size - bounds.width()) / 2f
        val y = (size + bounds.height()) / 2f
        
        // 绘制emoji
        canvas.drawText(emoji, x, y, textPaint)
        
        return bitmap
    }

    // 分析文本，找出所有emoji的位置
    fun findEmojiPositions(text: String): List<EmojiPosition> {
        val positions = mutableListOf<EmojiPosition>()
        var index = 0
        
        while (index < text.length) {
            if (isEmoji(text[index])) {
                val start = index
                // 处理组合emoji（如表情+肤色修饰符）
                while (index + 1 < text.length && 
                       (text[index + 1].code in 0xFE00..0xFE0F || // 变体选择器
                        text[index + 1].code in 0x1F3FB..0x1F3FF || // 肤色修饰符
                        text[index + 1].code == 0x200D)) { // 零宽连接符
                    index++
                    
                    // 如果是零宽连接符，需要再读取一个字符
                    if (text[index].code == 0x200D && index + 1 < text.length) {
                        index++
                    }
                }
                positions.add(EmojiPosition(start, index + 1, text.substring(start, index + 1)))
            }
            index++
        }
        
        return positions
    }
}

// 用于存储emoji在文本中的位置信息
data class EmojiPosition(
    val start: Int,
    val end: Int,
    val emoji: String
)