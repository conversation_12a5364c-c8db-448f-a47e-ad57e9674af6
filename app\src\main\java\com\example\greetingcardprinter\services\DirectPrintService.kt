package com.example.greetingcardprinter.services

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.pdf.PdfDocument
import android.os.Handler
import android.os.Looper
import android.util.Log
import com.example.greetingcardprinter.models.CardContent
import com.example.greetingcardprinter.models.PrinterInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.DataOutputStream
import java.io.IOException
import java.net.Socket
import java.nio.charset.Charset
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 直接打印服务，使用ESC/P命令集与爱普生打印机通信
 */
class DirectPrintService(private val context: Context) {
    companion object {
        private const val TAG = "DirectPrintService"
        private const val PRINTER_DEFAULT_PORT = 9100
        private const val BUFFER_SIZE = 8192

        // 页面尺寸（毫米转点数，1mm约等于8点）
        private const val PAGE_WIDTH_DOTS = 512  // 限制宽度为512点，许多打印机的最大支持宽度
        private const val PAGE_HEIGHT_DOTS = 680 // 85mm * 8
        private const val MARGIN_DOTS = 40       // 5mm * 8
        
        // ESC/P 命令常量
        private const val ESC = 0x1B
        private const val GS = 0x1D
        private const val FF = 0x0C
        private const val CR = 0x0D
        private const val LF = 0x0A

        // 字体和字符集相关命令
        private const val FONT_SIZE_NORMAL = 0x00
        private const val FONT_SIZE_DOUBLE_HEIGHT = 0x10
        private const val FONT_SIZE_DOUBLE_WIDTH = 0x20
        private const val FONT_SIZE_DOUBLE_BOTH = 0x30
    }

    private val isRunning = AtomicBoolean(false)
    private val printJobActive = AtomicBoolean(false)

    /**
     * 直接打印贺卡，无需预览界面
     */
    suspend fun printCardDirectly(
        content: CardContent,
        printerInfo: PrinterInfo
    ): Boolean = withContext(Dispatchers.IO) {
        if (isRunning.getAndSet(true)) {
            Log.w(TAG, "已有打印任务正在进行中")
            return@withContext false
        }

        try {
            Log.d(TAG, "开始直接打印贺卡: 内容长度=${content.textLines.size}行, 打印机=${printerInfo.serviceName}")
            Log.d(TAG, "打印机信息: id=${printerInfo.id}, 描述=${printerInfo.description}, 连接状态=${printerInfo.isConnected}")
            
            // 提取打印机IP地址
            var printerIp = extractPrinterIpAddress(printerInfo)
            
            if (printerIp.isEmpty()) {
                Log.e(TAG, "无法获取打印机IP地址")
                return@withContext false
            }
            
            Log.d(TAG, "提取的打印机IP地址: $printerIp")
            
            // 在发送打印数据前，先检查打印机是否可访问
            if (!isPrinterReachable(printerIp)) {
                Log.e(TAG, "打印机不可访问，IP: $printerIp")
                return@withContext false
            }
            
            // 创建打印数据
            val printData = createPrintData(content)
            
            // 发送到打印机
            val success = sendToPrinter(printData, printerInfo, content.copies)
            
            if (success) {
                Log.d(TAG, "直接打印成功")
            } else {
                Log.e(TAG, "直接打印失败")
            }
            
            return@withContext success
        } catch (e: Exception) {
            Log.e(TAG, "直接打印过程中发生异常", e)
            return@withContext false
        } finally {
            isRunning.set(false)
        }
    }

    /**
     * 提取打印机IP地址
     */
    private fun extractPrinterIpAddress(printerInfo: PrinterInfo): String {
        // 尝试从hostAddress获取
        val hostAddress = printerInfo.hostAddress
        if (!hostAddress.isNullOrEmpty() && isValidIpAddress(hostAddress)) {
            return hostAddress
        }
        
        // 尝试从id中提取IP地址
        val ipFromId = extractIpAddressFromString(printerInfo.id)
        if (ipFromId.isNotEmpty()) {
            return ipFromId
        }
        
        // 尝试从description中提取IP地址
        val ipFromDesc = extractIpAddressFromString(printerInfo.description)
        if (ipFromDesc.isNotEmpty()) {
            return ipFromDesc
        }
        
        // 如果以上方法都失败，尝试使用默认IP（仅用于测试）
        return ""
    }
    
    /**
     * 从字符串中提取IP地址
     */
    private fun extractIpAddressFromString(text: String): String {
        val ipPattern = Regex("\\b(?:\\d{1,3}\\.){3}\\d{1,3}\\b")
        val match = ipPattern.find(text)
        return match?.value ?: ""
    }
    
    /**
     * 验证IP地址格式是否正确
     */
    private fun isValidIpAddress(ip: String): Boolean {
        return ip.matches(Regex("\\b(?:\\d{1,3}\\.){3}\\d{1,3}\\b"))
    }
    
    /**
     * 检查打印机是否可访问
     */
    private fun isPrinterReachable(ip: String): Boolean {
        return try {
            // 首先尝试使用InetAddress.isReachable
            val address = java.net.InetAddress.getByName(ip)
            val reachable = address.isReachable(3000) // 3秒超时
            
            if (reachable) {
                Log.d(TAG, "打印机可通过ICMP访问: $ip")
                return true
            }
            
            // 如果ICMP不可达，尝试通过Socket连接打印端口来检测
            try {
                val socket = java.net.Socket()
                socket.connect(java.net.InetSocketAddress(ip, PRINTER_DEFAULT_PORT), 5000) // 5秒超时
                socket.close()
                Log.d(TAG, "打印机端口可访问: $ip:$PRINTER_DEFAULT_PORT")
                true
            } catch (e: Exception) {
                Log.e(TAG, "无法连接到打印机端口: $ip:$PRINTER_DEFAULT_PORT", e)
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查打印机可访问性时出错", e)
            false
        }
    }

    /**
     * 将文本内容渲染为位图
     */
    private fun renderContentToBitmap(content: CardContent): Bitmap {
        // 创建位图
        val bitmap = Bitmap.createBitmap(
            PAGE_WIDTH_DOTS,
            PAGE_HEIGHT_DOTS,
            Bitmap.Config.ARGB_8888
        )
        
        // 创建画布
        val canvas = Canvas(bitmap)
        
        // 设置白色背景
        canvas.drawColor(Color.WHITE)
        
        // 创建画笔
        val paint = android.graphics.Paint().apply {
            isAntiAlias = true
            textAlign = android.graphics.Paint.Align.CENTER
            color = Color.BLACK
            // 设置较粗的字体以确保打印清晰
            isFakeBoldText = true
        }
        
        // 计算中心X坐标
        val centerX = PAGE_WIDTH_DOTS / 2f
        
        // 根据内容长度动态调整字体大小
        content.textLines.forEachIndexed { index, line ->
            // 动态计算字体大小，确保字体足够大
            val fontSize = when {
                line.text.length <= 10 -> 60f
                line.text.length <= 20 -> 48f
                else -> 36f
            }
            paint.textSize = fontSize
            
            // 计算文本高度和Y坐标
            val textHeight = paint.fontMetrics.bottom - paint.fontMetrics.top
            val startY = MARGIN_DOTS + (index + 1) * (textHeight * content.lineSpacing)
            
            // 绘制文本
            canvas.drawText(line.text, centerX, startY, paint)
        }
        
        return bitmap
    }

    /**
     * 创建ESC/P打印数据
     */
    private fun createPrintData(content: CardContent): ByteArray {
        val outputStream = ByteArrayOutputStream()
        
        try {
            // 1. 初始化打印机
            outputStream.write(byteArrayOf(ESC.toByte(), '@'.code.toByte()))
            
            // 2. 设置行间距
            outputStream.write(byteArrayOf(ESC.toByte(), '3'.code.toByte(), 24))
            
            // 3. 居中对齐
            outputStream.write(byteArrayOf(ESC.toByte(), 'a'.code.toByte(), 1))
            
            // 4. 将内容渲染为位图
            val bitmap = renderContentToBitmap(content)
            
            // 5. 处理位图并发送
            outputStream.write(processImageData(bitmap))
            
            // 6. 走纸和切纸
            outputStream.write(byteArrayOf(
                ESC.toByte(), 'J'.code.toByte(), 100,  // 走纸100点
                GS.toByte(), 'V'.code.toByte(), 0,     // 切纸（无留边）
                FF.toByte()                            // 换页
            ))
            
        } catch (e: IOException) {
            Log.e(TAG, "创建打印数据时发生错误", e)
        }
        
        return outputStream.toByteArray()
    }
    
    /**
     * 处理图像数据，使用标准的位图方式
     */
    private fun processImageData(bitmap: Bitmap): ByteArray {
        val outputStream = ByteArrayOutputStream()
        
        try {
            // 缩小图像以符合打印机限制
            val width = bitmap.width
            val height = bitmap.height
            
            // 转换为灰度图像并加强对比度
            val bmpGrayscale = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
            val canvas = Canvas(bmpGrayscale)
            canvas.drawColor(Color.WHITE) // 白色背景
            
            // 在灰度位图上绘制原始位图
            canvas.drawBitmap(bitmap, 0f, 0f, null)
            
            // 按照8点行处理图像
            var y = 0
            while (y < height) {
                // 计算当前行高度（最多8点）
                val lineHeight = minOf(8, height - y)
                
                // 发送位图命令：ESC * m nL nH 选择位图模式
                // 使用0模式（单密度，8点/行）
                outputStream.write(byteArrayOf(
                    ESC.toByte(), '*'.code.toByte(), 0,
                    (width % 256).toByte(),
                    (width / 256).toByte()
                ))
                
                // 按行发送位图数据
                for (x in 0 until width) {
                    var data = 0
                    
                    // 打包8个点为一个字节
                    for (ybit in 0 until lineHeight) {
                        if (y + ybit < height) {
                            val pixel = bmpGrayscale.getPixel(x, y + ybit)
                            // 根据像素灰度值决定是否打印该点
                            val grayscale = (Color.red(pixel) + Color.green(pixel) + Color.blue(pixel)) / 3
                            if (grayscale < 128) { // 如果像素较暗则打印
                                data = data or (1 shl ybit)
                            }
                        }
                    }
                    outputStream.write(data)
                }
                
                // 换行
                outputStream.write(byteArrayOf(CR.toByte(), LF.toByte()))
                
                y += 8 // 移动到下一个8点块
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "处理图像数据时发生错误", e)
        }
        
        return outputStream.toByteArray()
    }
    
    /**
     * 将打印数据发送到打印机
     */
    private fun sendToPrinter(printData: ByteArray, printerInfo: PrinterInfo, copies: Int): Boolean {
        var socket: Socket? = null
        var outputStream: DataOutputStream? = null
        
        try {
            // 提取打印机IP地址
            var printerIp = extractPrinterIpAddress(printerInfo)
            
            if (printerIp.isEmpty()) {
                Log.e(TAG, "无法获取打印机IP地址")
                return false
            }
            
            Log.d(TAG, "连接到打印机: $printerIp:$PRINTER_DEFAULT_PORT")
            
            // 创建Socket连接
            socket = Socket()
            // 设置连接超时和SO_TIMEOUT
            socket.connect(java.net.InetSocketAddress(printerIp, PRINTER_DEFAULT_PORT), 5000)  // 5秒连接超时
            socket.soTimeout = 8000  // 8秒读取超时
            
            outputStream = DataOutputStream(socket.getOutputStream())
            
            // 检查连接是否成功
            if (!socket.isConnected) {
                Log.e(TAG, "无法连接到打印机: $printerIp:$PRINTER_DEFAULT_PORT")
                return false
            }
            
            Log.d(TAG, "成功连接到打印机，准备发送数据，数据大小: ${printData.size} 字节")
            
            // 打印指定份数
            var successfulCopies = 0
            repeat(copies) { copyIndex ->
                try {
                    // 分块发送数据，避免缓冲区溢出
                    val chunkSize = 1024
                    var offset = 0
                    
                    Log.d(TAG, "开始发送第 ${copyIndex + 1}/$copies 份")
                    
                    while (offset < printData.size) {
                        val length = minOf(chunkSize, printData.size - offset)
                        outputStream.write(printData, offset, length)
                        outputStream.flush()
                        offset += length
                        
                        // 每发送一块数据后短暂等待
                        Thread.sleep(20)
                    }
                    
                    // 发送结束命令
                    outputStream.write(byteArrayOf(0x1B, 0x4A, 120))  // 走纸
                    outputStream.write(byteArrayOf(0x1D, 0x56, 48, 0))  // 切纸
                    outputStream.flush()
                    
                    Log.d(TAG, "第 ${copyIndex + 1}/$copies 份数据发送完成")
                    successfulCopies++
                    
                    // 每份打印后等待较长时间，让打印机有时间处理
                    Thread.sleep(1000)
                } catch (e: Exception) {
                    Log.e(TAG, "发送第 ${copyIndex + 1}/$copies 份时发生错误", e)
                    // 继续尝试发送下一份
                }
            }
            
            // 如果至少有一份发送成功，则认为打印成功
            val success = successfulCopies > 0
            Log.d(TAG, "打印完成，成功发送 $successfulCopies/$copies 份")
            return success
            
        } catch (e: IOException) {
            Log.e(TAG, "发送数据到打印机时发生错误", e)
            return false
        } catch (e: InterruptedException) {
            Log.e(TAG, "打印过程被中断", e)
            return false
        } finally {
            try {
                outputStream?.close()
                socket?.close()
            } catch (e: IOException) {
                Log.e(TAG, "关闭资源时发生错误", e)
            }
        }
    }
    
    /**
     * 检查打印作业是否处于活动状态
     * 这是一个简单实现，因为DirectPrintService没有真正的PrintJob对象，
     * 我们只是假设打印作业在发送数据后立即变为活动状态
     */
    private fun isPrintJobActive(): Boolean {
        // 在这个简化实现中，我们假设一旦数据发送到打印机，打印作业就是活动的
        // 在实际应用中，可以根据打印机的响应或其他条件设置此标志
        printJobActive.set(true)
        return true
    }
} 