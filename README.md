# CardPrint - 卡片打印工具

一个简单易用的 Android 卡片打印应用，专门用于快速打印文本卡片。支持自动读取剪贴板内容，灵活的文本对齐方式，以及便捷的打印预览功能。

## 功能特点

- 📋 自动读取剪贴板内容
- 🖨️ 快速连接上次使用的打印机
- 📝 支持文本左对齐、居中和右对齐
- 👀 实时打印预览
- 🎨 可自定义字体大小
- 💾 自动保存打印机设置

## 技术栈

- 开发语言：Kotlin
- 最低 Android SDK 版本：21 (Android 5.0)
- 目标 Android SDK 版本：33 (Android 13)
- 构建工具：Gradle 8.0

## 安装说明

### 开发环境要求

- Android Studio Flamingo 或更高版本
- JDK 17 或更高版本
- Android SDK Build Tools 33.0.0 或更高版本

### 构建步骤

1. 克隆项目到本地：
   ```bash
   <NAME_EMAIL>:ufomaker/CardPrint.git
   ```

2. 在 Android Studio 中打开项目

3. 等待 Gradle 同步完成

4. 点击 "Run" 按钮或使用快捷键 `Shift + F10` 运行项目

## 使用说明

1. 启动应用后，会自动读取剪贴板内容（如果有）
2. 可以手动编辑文本内容
3. 使用工具栏按钮调整文本对齐方式
4. 点击打印预览按钮查看打印效果
5. 确认无误后点击打印按钮执行打印

### 文本格式说明

- 普通文本默认左对齐
- 以 "——" 开头的文本自动右对齐
- 使用工具栏按钮可以手动设置文本对齐方式

## 注意事项

- 首次使用需要授予剪贴板访问权限
- 请确保打印机已正确连接到设备
- 支持 EPSON 等主流打印机型号

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的卡片打印功能
- 实现自动读取剪贴板
- 添加打印预览功能

## 贡献指南

欢迎提交 Issue 和 Pull Request 来帮助改进项目。在提交 PR 之前，请确保：

1. 代码符合项目的编码规范
2. 所有测试用例通过
3. 更新相关文档

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件 