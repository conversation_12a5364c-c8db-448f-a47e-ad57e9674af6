package com.example.greetingcardprinter.activities

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.greetingcardprinter.adapters.PrinterAdapter
import com.example.greetingcardprinter.databinding.DialogPrinterSelectionBinding
import com.example.greetingcardprinter.models.PrinterInfo
import com.example.greetingcardprinter.viewmodels.MainViewModel
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

class PrinterSelectionDialogFragment : DialogFragment() {

    private var _binding: DialogPrinterSelectionBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: MainViewModel by activityViewModels()
    private lateinit var printerAdapter: PrinterAdapter
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = DialogPrinterSelectionBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupRecyclerView()
        setupButtons()
        observeViewModel()
        
        // 刷新打印机列表
        viewModel.startDiscovery()
    }
    
    private fun setupRecyclerView() {
        printerAdapter = PrinterAdapter { printer ->
            viewModel.selectPrinter(printer)
            dismiss()
        }
        
        binding.printerRecyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = printerAdapter
        }
    }
    
    private fun setupButtons() {
        binding.refreshButton.setOnClickListener {
            viewModel.startDiscovery()
        }
        
        binding.cancelButton.setOnClickListener {
            dismiss()
        }
    }
    
    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.discoveredPrinters.collectLatest { printers ->
                updatePrinterList(printers)
            }
        }
    }
    
    private fun updatePrinterList(printers: List<PrinterInfo>) {
        printerAdapter.submitList(printers)
        
        // 显示空列表提示
        binding.emptyView.visibility = if (printers.isEmpty()) View.VISIBLE else View.GONE
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
} 