package com.example.greetingcardprinter.activities

import android.app.AlertDialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.greetingcardprinter.adapters.PrinterAdapter
import com.example.greetingcardprinter.databinding.DialogPrinterSelectionBinding
import com.example.greetingcardprinter.models.PrinterInfo
import com.example.greetingcardprinter.viewmodels.MainViewModel
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

class PrinterSelectionDialogFragment : DialogFragment() {

    private var _binding: DialogPrinterSelectionBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: MainViewModel by activityViewModels()
    private lateinit var printerAdapter: PrinterAdapter
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = DialogPrinterSelectionBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupRecyclerView()
        setupButtons()
        observeViewModel()
        
        // 刷新打印机列表
        viewModel.startDiscovery()
    }
    
    private fun setupRecyclerView() {
        printerAdapter = PrinterAdapter { printer ->
            viewModel.selectPrinter(printer)
            dismiss()
        }
        
        binding.printerRecyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = printerAdapter
        }
    }
    
    private fun setupButtons() {
        binding.addManualButton.setOnClickListener {
            showAddManualPrinterDialog()
        }

        binding.refreshButton.setOnClickListener {
            viewModel.startDiscovery()
        }

        binding.cancelButton.setOnClickListener {
            dismiss()
        }
    }

    private fun showAddManualPrinterDialog() {
        val editText = EditText(requireContext()).apply {
            hint = "请输入打印机IP地址 (例如: *************)"
            setPadding(50, 30, 50, 30)
        }

        AlertDialog.Builder(requireContext())
            .setTitle("手动添加打印机")
            .setMessage("请输入打印机的IP地址:")
            .setView(editText)
            .setPositiveButton("添加") { _, _ ->
                val ip = editText.text.toString().trim()
                if (ip.isNotEmpty()) {
                    if (viewModel.addManualPrinter(ip)) {
                        Toast.makeText(requireContext(), "打印机添加成功", Toast.LENGTH_SHORT).show()
                    } else {
                        Toast.makeText(requireContext(), "打印机添加失败，请检查IP地址", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Toast.makeText(requireContext(), "请输入有效的IP地址", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.discoveredPrinters.collectLatest { printers ->
                updatePrinterList(printers)
            }
        }
    }
    
    private fun updatePrinterList(printers: List<PrinterInfo>) {
        printerAdapter.submitList(printers)
        
        // 显示空列表提示
        binding.emptyView.visibility = if (printers.isEmpty()) View.VISIBLE else View.GONE
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
} 