R_DEF: Internal format may change without notice
local
array font_families
color accent
color background_gray
color background_light
color black
color border_light
color error
color ic_launcher_background
color primary
color primary_dark
color purple_200
color purple_500
color purple_700
color teal_200
color teal_700
color text_hint
color text_primary
color text_secondary
color white
drawable apple_button_background
drawable apple_card_background
drawable apple_card_size_indicator_background
drawable apple_circle_button_background
drawable apple_rounded_button_background
drawable bg_template_info
drawable card_background
drawable card_size_indicator
drawable edit_text_background
drawable ic_add
drawable ic_align_center
drawable ic_align_left
drawable ic_align_right
drawable ic_clear
drawable ic_copy_decrease
drawable ic_copy_increase
drawable ic_delete
drawable ic_edit
drawable ic_expand_more
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_line_spacing_decrease
drawable ic_line_spacing_increase
drawable ic_remove
drawable ic_save
drawable ic_template
drawable ic_template_empty
drawable ic_text_decrease
drawable ic_text_increase
drawable preview_background
drawable rounded_background
id addImageButton
id addManualButton
id alignCenterButton
id alignLeftButton
id alignRightButton
id bottomButtonsLayout
id btnCancel
id btnConfirm
id btnSelectImage
id cancelButton
id cardSizeIndicator
id cardSizeInfoText
id clearTextButton
id closeButton
id copiesCountText
id copiesDecreaseButton
id copiesIncreaseButton
id deleteButton
id descriptionText
id dialogTitle
id editButton
id editText
id editorScrollView
id emptyStateLayout
id emptyView
id fontSizeDecreaseButton
id fontSizeIncreaseButton
id fontSizeText
id formatControlsContent
id formatControlsExpandIcon
id formatControlsHeader
id formatControlsLayout
id imagePreview
id imagePreviewContainer
id lineSpacingDecreaseButton
id lineSpacingIncreaseButton
id lineSpacingText
id menu_manage_templates
id menu_save_template
id previewImageView
id printButton
id printerAddressText
id printerNameText
id printerRecyclerView
id refreshButton
id removeImageButton
id saveButton
id seekBarWidth
id spinnerPosition
id templateContentEditText
id templateContentText
id templateInfoText
id templateNameEditText
id templateNameText
id templateTimeText
id templatesRecyclerView
id titleText
id toolbar
id tvHeightMm
id tvImageSize
id tvPositionDescription
id tvWidthPercentage
layout activity_image_picker
layout activity_main
layout activity_print_size_test
layout activity_template_manager
layout dialog_card_size_settings
layout dialog_printer_selection
layout dialog_template_edit
layout item_printer
layout item_template
menu main_menu
mipmap ic_launcher
mipmap ic_launcher_round
string align_center
string align_left
string align_right
string alignment
string app_name
string available_printers
string cancel
string card_size_label
string copies
string decrease_copies
string decrease_font_size
string decrease_line_spacing
string delete
string delete_template
string edit
string edit_template
string edit_text_description
string edit_text_hint
string editor_title
string enter_greeting_text
string font_family
string font_size
string increase_copies
string increase_font_size
string increase_line_spacing
string line_spacing
string manage_templates
string no_printer_selected
string no_printers_found
string no_templates
string no_templates_description
string preview_title
string print
string print_card
string print_preview
string refresh
string save
string save_template
string select_printer
string select_printer_title
string template_content
string template_manager_title
string template_name
style Theme.CardPrint
xml backup_rules
xml data_extraction_rules
xml file_paths
