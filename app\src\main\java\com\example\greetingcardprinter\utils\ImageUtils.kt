package com.example.greetingcardprinter.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.net.Uri
import android.util.Log
import androidx.exifinterface.media.ExifInterface
import java.io.IOException
import java.io.InputStream

/**
 * 图片处理工具类
 */
object ImageUtils {
    private const val TAG = "ImageUtils"
    
    /**
     * 从Uri加载Bitmap
     * 
     * @param context 上下文
     * @param uri 图片URI
     * @param maxWidthPx 最大宽度(像素)
     * @param maxHeightPx 最大高度(像素)
     * @return 加载并按需缩放的Bitmap，失败返回null
     */
    fun loadBitmapFromUri(
        context: Context, 
        uri: Uri, 
        maxWidthPx: Int, 
        maxHeightPx: Int
    ): Bitmap? {
        try {
            // 获取输入流
            context.contentResolver.openInputStream(uri)?.use { inputStream ->
                // 先解码图片尺寸
                val options = BitmapFactory.Options().apply {
                    inJustDecodeBounds = true
                }
                BitmapFactory.decodeStream(inputStream, null, options)
                
                // 计算采样率
                val sampleSize = calculateInSampleSize(options, maxWidthPx, maxHeightPx)
                
                // 重新使用输入流
                context.contentResolver.openInputStream(uri)?.use { newInputStream ->
                    // 使用采样率解码图片
                    val decodeOptions = BitmapFactory.Options().apply {
                        inSampleSize = sampleSize
                        inPreferredConfig = Bitmap.Config.ARGB_8888
                    }
                    
                    val bitmap = BitmapFactory.decodeStream(newInputStream, null, decodeOptions)
                    
                    // 处理图片旋转
                    return bitmap?.let { rotateImageIfNeeded(context, it, uri) }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "加载图片出错: ${e.message}", e)
        }
        
        return null
    }
    
    /**
     * 计算采样率，用于缩小图片
     */
    private fun calculateInSampleSize(
        options: BitmapFactory.Options, 
        reqWidth: Int, 
        reqHeight: Int
    ): Int {
        val height = options.outHeight
        val width = options.outWidth
        var inSampleSize = 1
        
        if (height > reqHeight || width > reqWidth) {
            val halfHeight = height / 2
            val halfWidth = width / 2
            
            // 计算最大的采样率，同时保持至少一个维度大于请求的尺寸
            while ((halfHeight / inSampleSize) >= reqHeight && (halfWidth / inSampleSize) >= reqWidth) {
                inSampleSize *= 2
            }
        }
        
        return inSampleSize
    }
    
    /**
     * 根据EXIF信息旋转图片
     */
    private fun rotateImageIfNeeded(context: Context, bitmap: Bitmap, uri: Uri): Bitmap {
        var rotatedBitmap = bitmap
        
        try {
            context.contentResolver.openInputStream(uri)?.use { inputStream ->
                // 尝试从EXIF信息中获取旋转角度
                val orientation = getExifOrientation(inputStream)
                
                if (orientation > 0) {
                    val matrix = Matrix()
                    matrix.postRotate(orientation.toFloat())
                    rotatedBitmap = Bitmap.createBitmap(
                        bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true
                    )
                    // 如果创建了新的位图，回收原始位图
                    if (rotatedBitmap != bitmap) {
                        bitmap.recycle()
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "旋转图片出错: ${e.message}", e)
        }
        
        return rotatedBitmap
    }
    
    /**
     * 获取图片的EXIF方向
     */
    private fun getExifOrientation(inputStream: InputStream): Int {
        try {
            val exif = ExifInterface(inputStream)
            val orientation = exif.getAttributeInt(
                ExifInterface.TAG_ORIENTATION,
                ExifInterface.ORIENTATION_NORMAL
            )
            
            return when (orientation) {
                ExifInterface.ORIENTATION_ROTATE_90 -> 90
                ExifInterface.ORIENTATION_ROTATE_180 -> 180
                ExifInterface.ORIENTATION_ROTATE_270 -> 270
                else -> 0
            }
        } catch (e: IOException) {
            Log.e(TAG, "读取EXIF信息失败: ${e.message}", e)
            return 0
        }
    }
    
    /**
     * 根据目标尺寸调整图片
     * 
     * @param bitmap 原始位图
     * @param targetWidthPx 目标宽度(像素)
     * @param targetHeightPx 目标高度(像素)
     * @param maintainAspectRatio 是否保持宽高比
     * @return 调整后的Bitmap
     */
    fun resizeBitmap(
        bitmap: Bitmap, 
        targetWidthPx: Int, 
        targetHeightPx: Int,
        maintainAspectRatio: Boolean = true
    ): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        
        val scaleWidth = targetWidthPx.toFloat() / width
        val scaleHeight = targetHeightPx.toFloat() / height
        
        val matrix = Matrix()
        
        if (maintainAspectRatio) {
            // 保持宽高比
            val scale = minOf(scaleWidth, scaleHeight)
            matrix.postScale(scale, scale)
            
            // 创建新的Bitmap，保持原始宽高比
            val scaledBitmap = Bitmap.createBitmap(
                bitmap, 0, 0, width, height, matrix, true
            )
            
            // 创建一个白色背景的Bitmap
            val result = Bitmap.createBitmap(targetWidthPx, targetHeightPx, Bitmap.Config.ARGB_8888)
            val canvas = android.graphics.Canvas(result)
            canvas.drawColor(android.graphics.Color.WHITE)
            
            // 将缩放后的Bitmap绘制到居中位置
            val left = (targetWidthPx - scaledBitmap.width) / 2f
            val top = (targetHeightPx - scaledBitmap.height) / 2f
            canvas.drawBitmap(scaledBitmap, left, top, null)
            
            // 回收中间Bitmap
            if (scaledBitmap != bitmap) {
                scaledBitmap.recycle()
            }
            
            return result
        } else {
            // 直接缩放到目标尺寸
            matrix.postScale(scaleWidth, scaleHeight)
            return Bitmap.createBitmap(
                bitmap, 0, 0, width, height, matrix, true
            )
        }
    }
} 