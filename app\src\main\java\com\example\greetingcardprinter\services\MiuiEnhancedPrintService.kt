package com.example.greetingcardprinter.services

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.pdf.PdfDocument
import android.os.Bundle
import android.os.CancellationSignal
import android.os.ParcelFileDescriptor
import android.print.PageRange
import android.print.PrintAttributes
import android.print.PrintDocumentAdapter
import android.print.PrintDocumentInfo
import android.print.PrintManager
import android.util.Log
import com.example.greetingcardprinter.activities.MainActivity
import com.example.greetingcardprinter.models.CardContent
import com.example.greetingcardprinter.models.PrinterInfo
import com.example.greetingcardprinter.utils.mmToPx
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import java.io.FileOutputStream
import java.io.IOException
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 专门针对小米MIUI设备优化的增强打印服务
 * 解决小米设备上标准打印框架的问题
 */
class MiuiEnhancedPrintService(
    private val originalPrintService: PrintService,
    private val context: Context
) {
    companion object {
        private const val TAG = "MiuiEnhancedPrint"
        private const val PRINT_VERIFICATION_TIMEOUT = 10000L // 10秒超时
    }
    
    /**
     * 使用验证的打印方法，确保打印真正成功或提供准确的失败反馈
     */
    suspend fun printWithVerification(
        content: CardContent,
        printer: PrinterInfo
    ): Boolean = withContext(Dispatchers.Main) {
        try {
            // 添加日志，记录打印开始
            Log.d(TAG, "开始小米设备增强打印: 内容长度=${content.textLines.size}行, 打印机=${printer.serviceName}")
            
            // 设置验证标志
            val printVerified = AtomicBoolean(false)
            val printCancelled = AtomicBoolean(false)
            val printCompleted = AtomicBoolean(false)
            
            // 使用系统打印管理器
            val printManager = context.getSystemService(Context.PRINT_SERVICE) as PrintManager
            val jobName = "Enhanced Print Job ${System.currentTimeMillis()}"
            
            val metadataBundle = Bundle().apply {
                putString("XIAOMI_PRINT_JOB", "TRUE") // 添加小米特定元数据
            }
            
            // 创建增强型打印适配器
            val enhancedAdapter = createEnhancedAdapter(
                content, 
                printVerified, 
                printCancelled,
                printCompleted
            )
            
            // 创建打印属性 - 使用A4尺寸
            val printAttributes = PrintAttributes.Builder()
                .setMediaSize(PrintAttributes.MediaSize.ISO_A4)
                .setColorMode(PrintAttributes.COLOR_MODE_COLOR)
                .setResolution(PrintAttributes.Resolution("default", "默认", 300, 300))
                .setMinMargins(PrintAttributes.Margins.NO_MARGINS)
                .build()
            
            // 启动打印作业
            val printJob = printManager.print(jobName, enhancedAdapter, printAttributes)
            
            if (printJob == null) {
                Log.e(TAG, "小米设备打印失败：打印管理器返回了null打印作业")
                return@withContext false
            }
            
            Log.d(TAG, "打印作业已提交，开始监控状态")
            
            // 等待打印验证或超时
            withContext(Dispatchers.IO) {
                val startTime = System.currentTimeMillis()
                var lastLogTime = 0L
                
                while (!printVerified.get() && !printCancelled.get() && !printCompleted.get() && 
                       System.currentTimeMillis() - startTime < PRINT_VERIFICATION_TIMEOUT) {
                    
                    // 限制日志输出频率
                    val currentTime = System.currentTimeMillis()
                    if (currentTime - lastLogTime > 1000) {
                        lastLogTime = currentTime
                        val elapsedTime = (currentTime - startTime) / 1000
                        Log.d(TAG, "等待打印结果: $elapsedTime 秒 (verified=${printVerified.get()}, cancelled=${printCancelled.get()}, completed=${printCompleted.get()})")
                    }
                    
                    delay(200)
                }
                
                // 检查打印状态
                val result = when {
                    printCompleted.get() -> {
                        Log.d(TAG, "打印已完成")
                        true
                    }
                    printCancelled.get() -> {
                        Log.e(TAG, "打印已取消")
                        false
                    }
                    printVerified.get() -> {
                        Log.d(TAG, "打印已验证，假定成功")
                        true
                    }
                    else -> {
                        Log.e(TAG, "打印验证超时")
                        false
                    }
                }
                
                // 只有在未完成且未取消的情况下，才尝试取消打印作业
                if (!printCompleted.get() && !printCancelled.get() && printJob.isStarted) {
                    try {
                        Log.d(TAG, "正在取消未完成的打印作业")
                        printJob.cancel()
                    } catch (e: Exception) {
                        Log.e(TAG, "取消打印作业时出错", e)
                    }
                }
                
                // 更新ViewModel状态
                withContext(Dispatchers.Main) {
                    try {
                        (context as? MainActivity)?.viewModel?.onPrintResult(
                            result, 
                            if (!result) "打印未能完成，请检查打印机" else ""
                        )
                    } catch (e: Exception) {
                        Log.e(TAG, "更新打印结果状态时出错", e)
                    }
                }
                
                return@withContext result
            }
        } catch (e: Exception) {
            Log.e(TAG, "增强打印过程中发生异常", e)
            return@withContext false
        }
    }
    
    /**
     * 创建增强型打印适配器，添加验证机制
     */
    private fun createEnhancedAdapter(
        content: CardContent,
        printVerified: AtomicBoolean,
        printCancelled: AtomicBoolean,
        printCompleted: AtomicBoolean
    ): PrintDocumentAdapter {
        return object : PrintDocumentAdapter() {
            private var pdfDocument: PdfDocument? = null
            
            override fun onLayout(
                oldAttributes: PrintAttributes?,
                newAttributes: PrintAttributes,
                cancellationSignal: CancellationSignal,
                callback: LayoutResultCallback,
                extras: Bundle?
            ) {
                if (cancellationSignal.isCanceled) {
                    callback.onLayoutCancelled()
                    printCancelled.set(true)
                    return
                }
                
                try {
                    Log.d(TAG, "onLayout: 准备布局打印内容")
                    
                    // 强制使用A4尺寸的打印属性
                    val forcedAttributes = PrintAttributes.Builder()
                        .setMediaSize(PrintAttributes.MediaSize.ISO_A4)
                        .setColorMode(newAttributes.colorMode ?: PrintAttributes.COLOR_MODE_COLOR)
                        .setResolution(newAttributes.resolution ?: PrintAttributes.Resolution("default", "默认", 300, 300))
                        .setMinMargins(PrintAttributes.Margins.NO_MARGINS)
                        .build()
                    
                    // 告诉系统我们有content.copies页
                    val info = PrintDocumentInfo.Builder("Enhanced Print ${System.currentTimeMillis()}")
                        .setContentType(PrintDocumentInfo.CONTENT_TYPE_DOCUMENT)
                        .setPageCount(content.copies)
                        .build()
                    
                    // 在此处设置验证标志
                    printVerified.set(true)
                    
                    callback.onLayoutFinished(info, !forcedAttributes.equals(oldAttributes))
                    Log.d(TAG, "onLayout: 布局已完成")
                    
                } catch (e: Exception) {
                    Log.e(TAG, "onLayout过程中发生异常", e)
                    callback.onLayoutFailed("布局失败: ${e.message}")
                    printCancelled.set(true)
                }
            }
            
            override fun onWrite(
                pages: Array<out PageRange>,
                destination: ParcelFileDescriptor,
                cancellationSignal: CancellationSignal,
                callback: WriteResultCallback
            ) {
                if (cancellationSignal.isCanceled) {
                    callback.onWriteCancelled()
                    printCancelled.set(true)
                    return
                }
                
                try {
                    Log.d(TAG, "onWrite: 开始写入打印内容")
                    
                    pdfDocument = PdfDocument()
                    
                    // 计算A4纸张的尺寸（像素）
                    val a4WidthMm = 210f
                    val a4HeightMm = 297f
                    val a4WidthPx = a4WidthMm.mmToPx(context).toInt()
                    val a4HeightPx = a4HeightMm.mmToPx(context).toInt()
                    
                    // 计算贺卡的尺寸（像素）
                    val cardWidthPx = content.cardWidth.mmToPx(context).toInt()
                    val cardHeightPx = content.cardHeight.mmToPx(context).toInt()
                    
                    // 计算贺卡在A4纸张上的位置
                    val leftMarginPx = ((a4WidthPx - cardWidthPx) / 2f).toInt()
                    val topMarginPx = (cardHeightPx + 10f.mmToPx(context)).toInt()
                    
                    // 生成多个页面，每页内容相同（模拟多份）
                    val pageRanges = mutableListOf<PageRange>()
                    
                    // 查找需要生成的页范围
                    for (pageRange in pages) {
                        for (i in pageRange.start..minOf(pageRange.end, content.copies - 1)) {
                            val pageInfo = PdfDocument.PageInfo.Builder(a4WidthPx, a4HeightPx, i).create()
                            val page = pdfDocument?.startPage(pageInfo)
                            
                            if (page != null) {
                                // 检查取消信号
                                if (cancellationSignal.isCanceled) {
                                    pdfDocument?.finishPage(page)
                                    callback.onWriteCancelled()
                                    printCancelled.set(true)
                                    return
                                }
                                
                                // 在此处使用原始PrintService的绘制方法
                                val canvas = page.canvas
                                
                                // 使用画布的withSave扩展函数保存状态
                                canvas.save()
                                try {
                                    // 清空画布（设置白色背景）
                                    canvas.drawColor(Color.WHITE)
                                    
                                    // 移动画布到贺卡绘制位置
                                    canvas.translate(leftMarginPx.toFloat(), topMarginPx.toFloat())
                                    
                                    // 绘制贺卡内容
                                    originalPrintService.drawCardOnCanvas(
                                        canvas, 
                                        content, 
                                        cardWidthPx.toFloat(), 
                                        cardHeightPx.toFloat()
                                    )
                                } finally {
                                    canvas.restore()
                                }
                                
                                pdfDocument?.finishPage(page)
                                pageRanges.add(PageRange(i, i))
                            }
                        }
                    }
                    
                    // 写入PDF文件
                    try {
                        pdfDocument?.writeTo(FileOutputStream(destination.fileDescriptor))
                        if (pageRanges.isNotEmpty()) {
                            callback.onWriteFinished(pageRanges.toTypedArray())
                            printCompleted.set(true)
                            Log.d(TAG, "onWrite: 内容已成功写入")
                        } else {
                            callback.onWriteFinished(arrayOf(PageRange.ALL_PAGES))
                            printCompleted.set(true)
                            Log.d(TAG, "onWrite: 内容已成功写入(所有页面)")
                        }
                    } catch (e: IOException) {
                        Log.e(TAG, "写入PDF文档失败", e)
                        callback.onWriteFailed(e.message)
                        printCancelled.set(true)
                    }
                    
                } catch (e: Exception) {
                    Log.e(TAG, "onWrite过程中发生异常", e)
                    callback.onWriteFailed("写入失败: ${e.message}")
                    printCancelled.set(true)
                } finally {
                    pdfDocument?.close()
                    pdfDocument = null
                }
            }
            
            override fun onFinish() {
                super.onFinish()
                
                try {
                    // 清理资源
                    pdfDocument?.close()
                    pdfDocument = null
                    
                    Log.d(TAG, "onFinish: 打印适配器已完成")
                } catch (e: Exception) {
                    Log.e(TAG, "onFinish过程中发生异常", e)
                }
            }
        }
    }
} 