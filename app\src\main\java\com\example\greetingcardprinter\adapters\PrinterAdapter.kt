package com.example.greetingcardprinter.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.greetingcardprinter.databinding.ItemPrinterBinding
import com.example.greetingcardprinter.models.PrinterInfo

class PrinterAdapter(
    private val onPrinterSelected: (PrinterInfo) -> Unit
) : ListAdapter<PrinterInfo, PrinterAdapter.PrinterViewHolder>(PrinterDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PrinterViewHolder {
        val binding = ItemPrinterBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return PrinterViewHolder(binding, onPrinterSelected)
    }

    override fun onBindViewHolder(holder: <PERSON><PERSON><PERSON><PERSON><PERSON>Holder, position: Int) {
        holder.bind(getItem(position))
    }

    class PrinterViewHolder(
        private val binding: ItemPrinterBinding,
        private val onPrinterSelected: (PrinterInfo) -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(printer: PrinterInfo) {
            binding.printerNameText.text = printer.serviceName
            binding.printerAddressText.text = printer.description
            
            binding.root.setOnClickListener {
                onPrinterSelected(printer)
            }
        }
    }

    private class PrinterDiffCallback : DiffUtil.ItemCallback<PrinterInfo>() {
        override fun areItemsTheSame(oldItem: PrinterInfo, newItem: PrinterInfo): Boolean {
            return oldItem.serviceName == newItem.serviceName
        }

        override fun areContentsTheSame(oldItem: PrinterInfo, newItem: PrinterInfo): Boolean {
            return oldItem == newItem
        }
    }
} 