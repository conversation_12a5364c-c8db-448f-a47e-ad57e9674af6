[{"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\menu_main_menu.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\menu\\main_menu.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_ic_template_empty.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\ic_template_empty.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\mipmap-hdpi_ic_launcher_round.png.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\mipmap-hdpi\\ic_launcher_round.png"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\layout_dialog_printer_selection.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\layout\\dialog_printer_selection.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_ic_copy_increase.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\ic_copy_increase.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_apple_button_background.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\apple_button_background.xml"}, {"merged": "com.example.greetingcardprinter.app-debug-37:/layout_dialog_printer_selection.xml.flat", "source": "com.example.greetingcardprinter.app-main-39:/layout/dialog_printer_selection.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_preview_background.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\preview_background.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\mipmap-xxhdpi_ic_launcher_round.png.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\mipmap-xxhdpi\\ic_launcher_round.png"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_ic_remove.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\ic_remove.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_ic_add.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\ic_add.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_apple_circle_button_background.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\apple_circle_button_background.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_ic_launcher_foreground.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_bg_template_info.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\bg_template_info.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_rounded_background.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\rounded_background.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_ic_align_left.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\ic_align_left.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\xml_file_paths.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\xml\\file_paths.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_card_size_indicator.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\card_size_indicator.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_ic_edit.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\ic_edit.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\mipmap-xhdpi_ic_launcher.png.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\mipmap-xhdpi\\ic_launcher.png"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_ic_expand_more.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\ic_expand_more.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\layout_dialog_card_size_settings.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\layout\\dialog_card_size_settings.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\mipmap-mdpi_ic_launcher_round.png.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\mipmap-mdpi\\ic_launcher_round.png"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\mipmap-xhdpi_ic_launcher_round.png.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\mipmap-xhdpi\\ic_launcher_round.png"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_ic_save.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\ic_save.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_apple_card_size_indicator_background.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\apple_card_size_indicator_background.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_ic_template.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\ic_template.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\layout_item_printer.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\layout\\item_printer.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\mipmap-xxxhdpi_ic_launcher.png.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\mipmap-xxxhdpi\\ic_launcher.png"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\layout_activity_image_picker.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\layout\\activity_image_picker.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\layout_dialog_template_edit.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\layout\\dialog_template_edit.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_ic_text_decrease.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\ic_text_decrease.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_ic_align_right.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\ic_align_right.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_apple_card_background.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\apple_card_background.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\mipmap-mdpi_ic_launcher.png.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\mipmap-mdpi\\ic_launcher.png"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_ic_delete.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\ic_delete.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_card_background.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\card_background.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\layout_activity_main.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\layout\\activity_main.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_ic_copy_decrease.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\ic_copy_decrease.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_apple_rounded_button_background.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\apple_rounded_button_background.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\xml_data_extraction_rules.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\xml\\data_extraction_rules.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_ic_launcher_background.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\ic_launcher_background.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\layout_activity_print_size_test.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\layout\\activity_print_size_test.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\layout_item_template.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\layout\\item_template.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\xml_backup_rules.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\xml\\backup_rules.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\mipmap-hdpi_ic_launcher.png.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\mipmap-hdpi\\ic_launcher.png"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_ic_align_center.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\ic_align_center.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\mipmap-xxxhdpi_ic_launcher_round.png.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\mipmap-xxxhdpi\\ic_launcher_round.png"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_ic_text_increase.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\ic_text_increase.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_edit_text_background.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\edit_text_background.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_ic_line_spacing_decrease.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\ic_line_spacing_decrease.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\layout_activity_template_manager.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\layout\\activity_template_manager.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_ic_line_spacing_increase.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\ic_line_spacing_increase.xml"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\mipmap-xxhdpi_ic_launcher.png.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\mipmap-xxhdpi\\ic_launcher.png"}, {"merged": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-debug-37:\\drawable_ic_clear.xml.flat", "source": "D:\\CardPrint2\\daemon\\8.10.2\\com.example.greetingcardprinter.app-main-39:\\drawable\\ic_clear.xml"}]