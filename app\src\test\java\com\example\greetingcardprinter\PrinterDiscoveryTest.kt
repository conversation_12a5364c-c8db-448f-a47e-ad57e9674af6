package com.example.greetingcardprinter

import org.junit.Test
import org.junit.Assert.*

/**
 * 打印机发现服务测试
 */
class PrinterDiscoveryTest {

    @Test
    fun testIpAddressValidation() {
        // 测试IP地址验证逻辑
        val validIPs = listOf(
            "*************",
            "********",
            "**********"
        )
        
        val invalidIPs = listOf(
            "256.256.256.256",
            "192.168.1",
            "not.an.ip",
            ""
        )
        
        validIPs.forEach { ip ->
            assertTrue("$ip should be valid", isValidIpAddress(ip))
        }
        
        invalidIPs.forEach { ip ->
            assertFalse("$ip should be invalid", isValidIpAddress(ip))
        }
    }
    
    @Test
    fun testPrinterKeywordDetection() {
        val printerNames = listOf(
            "HP LaserJet Pro",
            "Canon PIXMA",
            "Epson Printer",
            "Brother HL-L2350DW",
            "Samsung Xpress"
        )
        
        val nonPrinterNames = listOf(
            "Router",
            "Smart TV",
            "Phone",
            "Computer"
        )
        
        printerNames.forEach { name ->
            assertTrue("$name should be detected as printer", isPotentialPrinter(name))
        }
        
        nonPrinterNames.forEach { name ->
            assertFalse("$name should not be detected as printer", isPotentialPrinter(name))
        }
    }
    
    private fun isValidIpAddress(ip: String): Boolean {
        return try {
            val parts = ip.split(".")
            if (parts.size != 4) return false
            parts.all { part ->
                val num = part.toIntOrNull()
                num != null && num in 0..255
            }
        } catch (e: Exception) {
            false
        }
    }
    
    private fun isPotentialPrinter(serviceName: String): Boolean {
        val lowerServiceName = serviceName.lowercase()
        val printerKeywords = listOf(
            "printer", "print", "canon", "hp", "epson", "brother", "samsung", 
            "xerox", "lexmark", "ricoh", "kyocera", "sharp", "konica", "minolta",
            "ipp", "cups", "airprint", "打印", "印刷"
        )
        
        return printerKeywords.any { keyword ->
            lowerServiceName.contains(keyword)
        }
    }
}
