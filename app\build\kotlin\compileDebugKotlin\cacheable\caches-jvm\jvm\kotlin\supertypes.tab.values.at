/ Header Record For PersistentHashMapValueStorage) (androidx.appcompat.app.AppCompatActivity% $androidx.fragment.app.DialogFragment) (androidx.appcompat.app.AppCompatActivity) (androidx.appcompat.app.AppCompatActivity% $androidx.fragment.app.DialogFragment) (androidx.appcompat.app.AppCompatActivity) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback kotlin.Enum kotlin.Enum kotlin.Enum$ #androidx.lifecycle.AndroidViewModel: 9com.example.greetingcardprinter.viewmodels.PrintingStatus: 9com.example.greetingcardprinter.viewmodels.PrintingStatus: 9com.example.greetingcardprinter.viewmodels.PrintingStatus: 9com.example.greetingcardprinter.viewmodels.PrintingStatusC Bcom.example.greetingcardprinter.viewmodels.TemplateOperationStatusC Bcom.example.greetingcardprinter.viewmodels.TemplateOperationStatusC Bcom.example.greetingcardprinter.viewmodels.TemplateOperationStatusC Bcom.example.greetingcardprinter.viewmodels.TemplateOperationStatusC Bcom.example.greetingcardprinter.viewmodels.TemplateOperationStatusC Bcom.example.greetingcardprinter.viewmodels.TemplateOperationStatus!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding