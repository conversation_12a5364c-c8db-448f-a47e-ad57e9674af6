package com.example.greetingcardprinter.services

import android.content.Context
import android.net.nsd.NsdManager
import android.net.nsd.NsdServiceInfo
import android.os.Handler
import android.os.Looper
import android.util.Log
import com.example.greetingcardprinter.models.PrinterInfo
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

class PrinterDiscoveryService(private val context: Context) {
    private val nsdManager: NsdManager by lazy {
        context.getSystemService(Context.NSD_SERVICE) as NsdManager
    }

    private val _printers = MutableStateFlow<List<PrinterInfo>>(emptyList())
    val printers: StateFlow<List<PrinterInfo>> = _printers.asStateFlow()

    private var discoveryListener: NsdManager.DiscoveryListener? = null
    private var resolveListener: NsdManager.ResolveListener? = null
    private var isDiscovering = false
    
    // 添加自动重连机制
    private var autoReconnectRunnable: Runnable? = null
    private var autoReconnectHandler: Handler? = null
    private val AUTO_RECONNECT_INTERVAL = 30000L // 30秒自动重连间隔

    // 添加打印机缓存机制，将最近丢失的打印机缓存起来
    // 键是打印机服务名称，值是缓存的打印机信息和该打印机的Handler用于延迟移除
    private val printerCache = mutableMapOf<String, Pair<PrinterInfo, Handler>>()
    // 缓存有效期，单位毫秒（增加到30秒）
    private val CACHE_VALIDITY_PERIOD = 30000L
    // 主线程Handler，用于延迟执行
    private val mainHandler = Handler(Looper.getMainLooper())

    companion object {
        private const val TAG = "PrinterDiscoveryService"
        private const val SERVICE_TYPE = "_ipp._tcp."
    }

    fun startDiscovery() {
        if (isDiscovering) {
            return
        }

        // 清理之前的监听器
        stopDiscovery()

        // 创建新的解析监听器
        resolveListener = createResolveListener()

        // 创建新的发现监听器
        discoveryListener = object : NsdManager.DiscoveryListener {
            override fun onStartDiscoveryFailed(serviceType: String, errorCode: Int) {
                isDiscovering = false
                scheduleReconnect()
                stopDiscovery()
            }

            override fun onStopDiscoveryFailed(serviceType: String, errorCode: Int) {
                isDiscovering = false
            }

            override fun onDiscoveryStarted(serviceType: String) {
                isDiscovering = true
                // 清空之前的打印机列表
                _printers.value = emptyList()
                // 清空缓存
                synchronized(printerCache) {
                    printerCache.forEach { (_, pair) ->
                        pair.second.removeCallbacksAndMessages(null)
                    }
                    printerCache.clear()
                }
                
                // 取消可能存在的重连任务
                cancelReconnect()
            }

            override fun onDiscoveryStopped(serviceType: String) {
                isDiscovering = false
                
                // 如果不是主动停止，尝试重连
                if (autoReconnectRunnable != null) {
                    scheduleReconnect()
                }
            }

            override fun onServiceFound(serviceInfo: NsdServiceInfo) {
                val serviceName = serviceInfo.serviceName
                
                // 检查是否在缓存中
                synchronized(printerCache) {
                    if (printerCache.containsKey(serviceName)) {
                        // 如果是最近丢失的打印机重新被发现，取消延迟移除任务
                        val (cachedPrinter, handler) = printerCache[serviceName]!!
                        handler.removeCallbacksAndMessages(null)
                        
                        // 如果打印机不在当前列表中，重新添加
                        if (!_printers.value.any { it.serviceName == serviceName }) {
                            _printers.value = _printers.value + cachedPrinter
                        }
                        
                        // 从缓存中移除
                        printerCache.remove(serviceName)
                        return
                    }
                }
                
                // 如果不在缓存中，正常解析服务
                resolveListener?.let { listener ->
                    try {
                        nsdManager.resolveService(serviceInfo, listener)
                    } catch (e: Exception) {
                        Log.e(TAG, "解析服务失败", e)
                    }
                }
            }

            override fun onServiceLost(serviceInfo: NsdServiceInfo) {
                val serviceName = serviceInfo.serviceName
                
                // 查找当前打印机列表中的打印机
                val printer = _printers.value.find { it.serviceName == serviceName }
                if (printer != null) {
                    // 重要：不从当前列表中移除打印机
                    // 只将其加入缓存以防后续真正丢失
                    synchronized(printerCache) {
                        // 如果打印机已经在缓存中，先移除旧的延迟任务
                        printerCache[serviceName]?.second?.removeCallbacksAndMessages(null)
                        
                        // 创建新的延迟任务
                        val handler = Handler(Looper.getMainLooper())
                        val runnable = Runnable {
                            synchronized(printerCache) {
                                // 只有在缓存中存在且确实没有重新发现时才移除
                                if (printerCache.containsKey(serviceName) && 
                                    !_printers.value.any { it.serviceName == serviceName }) {
                                    _printers.value = _printers.value.filterNot { it.serviceName == serviceName }
                                    printerCache.remove(serviceName)
                                }
                            }
                        }
                        
                        // 缓存打印机信息和Handler
                        printerCache[serviceName] = Pair(printer, handler)
                        // 延长延迟时间到60秒，给予更多重连机会
                        handler.postDelayed(runnable, 60000L) // 1分钟
                    }
                }
            }
        }

        // 启动服务发现
        try {
            discoveryListener?.let { listener ->
                nsdManager.discoverServices(SERVICE_TYPE, NsdManager.PROTOCOL_DNS_SD, listener)
            }
        } catch (e: Exception) {
            Log.e(TAG, "启动服务发现时发生错误", e)
            isDiscovering = false
            scheduleReconnect()
        }
    }
    
    // 设置自动重连机制
    private fun scheduleReconnect() {
        cancelReconnect()
        
        autoReconnectHandler = Handler(Looper.getMainLooper())
        autoReconnectRunnable = Runnable {
            startDiscovery()
        }
        
        autoReconnectHandler?.postDelayed(autoReconnectRunnable!!, AUTO_RECONNECT_INTERVAL)
    }
    
    private fun cancelReconnect() {
        autoReconnectRunnable?.let {
            autoReconnectHandler?.removeCallbacks(it)
            autoReconnectRunnable = null
            autoReconnectHandler = null
        }
    }

    private fun createResolveListener(): NsdManager.ResolveListener {
        return object : NsdManager.ResolveListener {
            override fun onResolveFailed(serviceInfo: NsdServiceInfo, errorCode: Int) {
                Log.e(TAG, "服务解析失败: ${serviceInfo.serviceName}, errorCode=$errorCode")
            }

            override fun onServiceResolved(serviceInfo: NsdServiceInfo) {
                val printerInfo = PrinterInfo(
                    id = serviceInfo.serviceName,
                    serviceName = serviceInfo.serviceName,
                    hostAddress = serviceInfo.host.hostAddress ?: "",
                    port = serviceInfo.port
                )

                // 检查打印机是否已存在
                val existingPrinters = _printers.value
                if (!existingPrinters.any { it.id == printerInfo.id }) {
                    _printers.value = existingPrinters + printerInfo
                }
            }
        }
    }

    fun stopDiscovery() {
        cancelReconnect()
        
        try {
            synchronized(printerCache) {
                printerCache.forEach { (_, pair) ->
                    pair.second.removeCallbacksAndMessages(null)
                }
                printerCache.clear()
            }
            
            discoveryListener?.let { listener ->
                try {
                    nsdManager.stopServiceDiscovery(listener)
                } catch (e: Exception) {
                    Log.e(TAG, "停止服务发现时发生错误", e)
                }
            }
        } finally {
            discoveryListener = null
            resolveListener = null
            isDiscovering = false
            _printers.value = emptyList()
        }
    }
    
    fun restoreCachedPrinters(): Boolean {
        synchronized(printerCache) {
            if (printerCache.isEmpty()) {
                return false
            }
            
            val restoredPrinters = mutableListOf<PrinterInfo>()
            printerCache.forEach { (serviceName, pair) ->
                val (printer, handler) = pair
                handler.removeCallbacksAndMessages(null)
                restoredPrinters.add(printer)
            }
            
            if (restoredPrinters.isNotEmpty()) {
                val currentPrinters = _printers.value.toMutableList()
                restoredPrinters.forEach { printer ->
                    if (!currentPrinters.any { it.serviceName == printer.serviceName }) {
                        currentPrinters.add(printer)
                    }
                }
                _printers.value = currentPrinters
                
                printerCache.clear()
                return true
            }
            
            return false
        }
    }

    fun onDestroy() {
        stopDiscovery()
    }
}