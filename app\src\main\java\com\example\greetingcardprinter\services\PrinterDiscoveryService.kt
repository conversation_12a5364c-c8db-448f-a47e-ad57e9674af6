package com.example.greetingcardprinter.services

import android.content.Context
import android.net.nsd.NsdManager
import android.net.nsd.NsdServiceInfo
import android.os.Handler
import android.os.Looper
import android.util.Log
import com.example.greetingcardprinter.models.PrinterInfo
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

class PrinterDiscoveryService(private val context: Context) {
    private val nsdManager: NsdManager by lazy {
        context.getSystemService(Context.NSD_SERVICE) as NsdManager
    }

    private val _printers = MutableStateFlow<List<PrinterInfo>>(emptyList())
    val printers: StateFlow<List<PrinterInfo>> = _printers.asStateFlow()

    private var discoveryListener: NsdManager.DiscoveryListener? = null
    private var printerDiscoveryListener: NsdManager.DiscoveryListener? = null
    private var httpDiscoveryListener: NsdManager.DiscoveryListener? = null
    private var isDiscovering = false
    private val activeServiceTypes = mutableSetOf<String>()

    // 为每个服务解析创建独立的监听器队列
    private val resolveQueue = mutableListOf<NsdServiceInfo>()
    private var isResolving = false
    
    // 添加自动重连机制
    private var autoReconnectRunnable: Runnable? = null
    private var autoReconnectHandler: Handler? = null
    private val AUTO_RECONNECT_INTERVAL = 30000L // 30秒自动重连间隔

    // 添加打印机缓存机制，将最近丢失的打印机缓存起来
    // 键是打印机服务名称，值是缓存的打印机信息和该打印机的Handler用于延迟移除
    private val printerCache = mutableMapOf<String, Pair<PrinterInfo, Handler>>()
    // 缓存有效期，单位毫秒（增加到30秒）
    private val CACHE_VALIDITY_PERIOD = 30000L
    // 主线程Handler，用于延迟执行
    private val mainHandler = Handler(Looper.getMainLooper())

    companion object {
        private const val TAG = "PrinterDiscoveryService"
        private const val SERVICE_TYPE = "_ipp._tcp."
        private const val SERVICE_TYPE_PRINTER = "_printer._tcp."
        private const val SERVICE_TYPE_HTTP = "_http._tcp."
        private const val SERVICE_TYPE_PDLDATA = "_pdl-datastream._tcp."
    }

    fun startDiscovery() {
        if (isDiscovering) {
            return
        }

        // 清理之前的监听器
        stopDiscovery()

        // 启动多种服务类型的发现
        startDiscoveryForServiceType(SERVICE_TYPE)
        startDiscoveryForServiceType(SERVICE_TYPE_PRINTER)
        startDiscoveryForServiceType(SERVICE_TYPE_HTTP)
    }

    private fun startDiscoveryForServiceType(serviceType: String) {
        // 创建新的发现监听器
        val discoveryListener = object : NsdManager.DiscoveryListener {
            override fun onStartDiscoveryFailed(serviceType: String, errorCode: Int) {
                Log.e(TAG, "服务发现启动失败: $serviceType, errorCode=$errorCode")
                activeServiceTypes.remove(serviceType)
                if (activeServiceTypes.isEmpty()) {
                    isDiscovering = false
                    scheduleReconnect()
                }
            }

            override fun onStopDiscoveryFailed(serviceType: String, errorCode: Int) {
                Log.e(TAG, "服务发现停止失败: $serviceType, errorCode=$errorCode")
                activeServiceTypes.remove(serviceType)
                if (activeServiceTypes.isEmpty()) {
                    isDiscovering = false
                }
            }

            override fun onDiscoveryStarted(serviceType: String) {
                Log.d(TAG, "开始发现服务类型: $serviceType")
                activeServiceTypes.add(serviceType)
                isDiscovering = true

                // 只在第一个服务类型开始时清空列表和缓存
                if (activeServiceTypes.size == 1) {
                    // 清空之前的打印机列表
                    _printers.value = emptyList()
                    // 清空缓存
                    synchronized(printerCache) {
                        printerCache.forEach { (_, pair) ->
                            pair.second.removeCallbacksAndMessages(null)
                        }
                        printerCache.clear()
                    }

                    // 取消可能存在的重连任务
                    cancelReconnect()
                }
            }

            override fun onDiscoveryStopped(serviceType: String) {
                Log.d(TAG, "停止发现服务类型: $serviceType")
                activeServiceTypes.remove(serviceType)

                if (activeServiceTypes.isEmpty()) {
                    isDiscovering = false

                    // 如果不是主动停止，尝试重连
                    if (autoReconnectRunnable != null) {
                        scheduleReconnect()
                    }
                }
            }

            override fun onServiceFound(serviceInfo: NsdServiceInfo) {
                val serviceName = serviceInfo.serviceName
                Log.d(TAG, "发现服务: $serviceName (类型: ${serviceInfo.serviceType})")

                // 过滤掉明显不是打印机的服务
                if (!isPotentialPrinter(serviceName, serviceInfo.serviceType)) {
                    Log.d(TAG, "跳过非打印机服务: $serviceName")
                    return
                }

                // 检查是否在缓存中
                synchronized(printerCache) {
                    if (printerCache.containsKey(serviceName)) {
                        // 如果是最近丢失的打印机重新被发现，取消延迟移除任务
                        val (cachedPrinter, handler) = printerCache[serviceName]!!
                        handler.removeCallbacksAndMessages(null)

                        // 如果打印机不在当前列表中，重新添加
                        if (!_printers.value.any { it.serviceName == serviceName }) {
                            _printers.value = _printers.value + cachedPrinter
                        }

                        // 从缓存中移除
                        printerCache.remove(serviceName)
                        return
                    }
                }

                // 如果不在缓存中，加入解析队列
                synchronized(resolveQueue) {
                    // 检查是否已经在队列中（避免重复解析同一个服务）
                    if (!resolveQueue.any { it.serviceName == serviceName }) {
                        resolveQueue.add(serviceInfo)
                        Log.d(TAG, "服务加入解析队列: $serviceName")
                    }
                }

                // 处理解析队列
                processResolveQueue()
            }

            override fun onServiceLost(serviceInfo: NsdServiceInfo) {
                val serviceName = serviceInfo.serviceName
                
                // 查找当前打印机列表中的打印机
                val printer = _printers.value.find { it.serviceName == serviceName }
                if (printer != null) {
                    // 重要：不从当前列表中移除打印机
                    // 只将其加入缓存以防后续真正丢失
                    synchronized(printerCache) {
                        // 如果打印机已经在缓存中，先移除旧的延迟任务
                        printerCache[serviceName]?.second?.removeCallbacksAndMessages(null)
                        
                        // 创建新的延迟任务
                        val handler = Handler(Looper.getMainLooper())
                        val runnable = Runnable {
                            synchronized(printerCache) {
                                // 只有在缓存中存在且确实没有重新发现时才移除
                                if (printerCache.containsKey(serviceName) && 
                                    !_printers.value.any { it.serviceName == serviceName }) {
                                    _printers.value = _printers.value.filterNot { it.serviceName == serviceName }
                                    printerCache.remove(serviceName)
                                }
                            }
                        }
                        
                        // 缓存打印机信息和Handler
                        printerCache[serviceName] = Pair(printer, handler)
                        // 延长延迟时间到60秒，给予更多重连机会
                        handler.postDelayed(runnable, 60000L) // 1分钟
                    }
                }
            }
        }

        // 启动服务发现
        try {
            nsdManager.discoverServices(serviceType, NsdManager.PROTOCOL_DNS_SD, discoveryListener)
            Log.d(TAG, "已启动服务发现: $serviceType")
        } catch (e: Exception) {
            Log.e(TAG, "启动服务发现时发生错误: $serviceType", e)
            activeServiceTypes.remove(serviceType)
            if (activeServiceTypes.isEmpty()) {
                isDiscovering = false
                scheduleReconnect()
            }
        }
    }
    
    // 设置自动重连机制
    private fun scheduleReconnect() {
        cancelReconnect()
        
        autoReconnectHandler = Handler(Looper.getMainLooper())
        autoReconnectRunnable = Runnable {
            startDiscovery()
        }
        
        autoReconnectHandler?.postDelayed(autoReconnectRunnable!!, AUTO_RECONNECT_INTERVAL)
    }
    
    private fun cancelReconnect() {
        autoReconnectRunnable?.let {
            autoReconnectHandler?.removeCallbacks(it)
            autoReconnectRunnable = null
            autoReconnectHandler = null
        }
    }

    private fun isPotentialPrinter(serviceName: String, serviceType: String): Boolean {
        val lowerServiceName = serviceName.lowercase()

        // 检查服务名称是否包含打印机相关关键词
        val printerKeywords = listOf(
            "printer", "print", "canon", "hp", "epson", "brother", "samsung",
            "xerox", "lexmark", "ricoh", "kyocera", "sharp", "konica", "minolta",
            "ipp", "cups", "airprint", "打印", "印刷"
        )

        val hasKeyword = printerKeywords.any { keyword ->
            lowerServiceName.contains(keyword)
        }

        // 检查服务类型
        val isPrinterServiceType = serviceType.contains("_ipp._tcp") ||
                                  serviceType.contains("_printer._tcp") ||
                                  serviceType.contains("_pdl-datastream._tcp")

        return hasKeyword || isPrinterServiceType
    }

    /**
     * 处理解析队列，一次只解析一个服务
     */
    private fun processResolveQueue() {
        synchronized(resolveQueue) {
            if (isResolving || resolveQueue.isEmpty()) {
                return
            }

            isResolving = true
            val serviceInfo = resolveQueue.removeAt(0)

            Log.d(TAG, "开始解析服务: ${serviceInfo.serviceName}")

            val resolveListener = object : NsdManager.ResolveListener {
                override fun onResolveFailed(serviceInfo: NsdServiceInfo, errorCode: Int) {
                    Log.e(TAG, "服务解析失败: ${serviceInfo.serviceName}, errorCode=$errorCode")
                    isResolving = false
                    // 继续处理队列中的下一个服务
                    processResolveQueue()
                }

                override fun onServiceResolved(serviceInfo: NsdServiceInfo) {
                    Log.d(TAG, "服务解析成功: ${serviceInfo.serviceName}")

                    val printerInfo = PrinterInfo(
                        id = serviceInfo.serviceName,
                        serviceName = serviceInfo.serviceName,
                        hostAddress = serviceInfo.host?.hostAddress ?: "",
                        port = serviceInfo.port,
                        description = "网络发现",
                        isConnected = true
                    )

                    // 检查打印机是否已存在
                    val existingPrinters = _printers.value
                    if (!existingPrinters.any { it.id == printerInfo.id }) {
                        _printers.value = existingPrinters + printerInfo
                        Log.d(TAG, "添加新打印机: ${printerInfo.serviceName} (${printerInfo.hostAddress})")
                    }

                    isResolving = false
                    // 继续处理队列中的下一个服务
                    processResolveQueue()
                }
            }

            try {
                nsdManager.resolveService(serviceInfo, resolveListener)
            } catch (e: Exception) {
                Log.e(TAG, "启动服务解析失败: ${serviceInfo.serviceName}", e)
                isResolving = false
                // 继续处理队列中的下一个服务
                processResolveQueue()
            }
        }
    }



    fun stopDiscovery() {
        cancelReconnect()

        try {
            synchronized(printerCache) {
                printerCache.forEach { (_, pair) ->
                    pair.second.removeCallbacksAndMessages(null)
                }
                printerCache.clear()
            }

            // 停止所有活跃的服务发现
            val listenersToStop = mutableListOf<NsdManager.DiscoveryListener>()
            discoveryListener?.let { listenersToStop.add(it) }
            printerDiscoveryListener?.let { listenersToStop.add(it) }
            httpDiscoveryListener?.let { listenersToStop.add(it) }

            listenersToStop.forEach { listener ->
                try {
                    nsdManager.stopServiceDiscovery(listener)
                } catch (e: Exception) {
                    Log.e(TAG, "停止服务发现时发生错误", e)
                }
            }
        } finally {
            discoveryListener = null
            printerDiscoveryListener = null
            httpDiscoveryListener = null
            isDiscovering = false
            isResolving = false
            activeServiceTypes.clear()
            synchronized(resolveQueue) {
                resolveQueue.clear()
            }
            _printers.value = emptyList()
        }
    }
    
    fun restoreCachedPrinters(): Boolean {
        synchronized(printerCache) {
            if (printerCache.isEmpty()) {
                return false
            }
            
            val restoredPrinters = mutableListOf<PrinterInfo>()
            printerCache.forEach { (serviceName, pair) ->
                val (printer, handler) = pair
                handler.removeCallbacksAndMessages(null)
                restoredPrinters.add(printer)
            }
            
            if (restoredPrinters.isNotEmpty()) {
                val currentPrinters = _printers.value.toMutableList()
                restoredPrinters.forEach { printer ->
                    if (!currentPrinters.any { it.serviceName == printer.serviceName }) {
                        currentPrinters.add(printer)
                    }
                }
                _printers.value = currentPrinters
                
                printerCache.clear()
                return true
            }
            
            return false
        }
    }

    /**
     * 手动添加打印机（用于网络发现失败时的备用方案）
     */
    fun addManualPrinter(ip: String, name: String = "手动添加的打印机"): Boolean {
        try {
            // 验证IP地址格式
            val address = java.net.InetAddress.getByName(ip)

            // 检查是否已存在
            val existingPrinters = _printers.value
            if (existingPrinters.any { it.hostAddress == ip }) {
                Log.d(TAG, "打印机已存在: $ip")
                return false
            }

            val printerInfo = PrinterInfo(
                id = "manual_$ip",
                serviceName = "$name ($ip)",
                hostAddress = ip,
                port = 631, // IPP默认端口
                description = "手动添加",
                isConnected = true
            )

            _printers.value = existingPrinters + printerInfo
            Log.d(TAG, "手动添加打印机成功: $ip")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "手动添加打印机失败: $ip", e)
            return false
        }
    }

    /**
     * 扫描常见的打印机IP地址
     */
    fun scanCommonPrinterIPs() {
        // 获取当前网络的网段
        val commonIPs = listOf(
            "*************", "*************", "*************", "*************",
            "*************", "*************", "*************", "*************",
            "**********", "**********", "**********", "**********"
        )

        Log.d(TAG, "开始扫描常见打印机IP地址")

        commonIPs.forEach { ip ->
            try {
                // 简单的连通性检查
                val address = java.net.InetAddress.getByName(ip)
                if (address.isReachable(1000)) { // 1秒超时
                    addManualPrinter(ip, "发现的打印机")
                }
            } catch (e: Exception) {
                // 忽略连接失败的IP
            }
        }
    }

    fun onDestroy() {
        stopDiscovery()
    }
}