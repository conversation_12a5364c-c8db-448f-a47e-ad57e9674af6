<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BloomCard - 花店贺卡打印助手</title>
  <style>
    :root {
      /* 主色调 */
      --primary: #FF6B95;  /* 粉红色 */
      --primary-dark: #E84C7B;
      --primary-light: #FFB5C9;
      
      /* 辅助色 */
      --accent-green: #4CAF50;
      --accent-purple: #9C27B0;
      --accent-blue: #2196F3;
      
      /* 背景色 */
      --bg-light: #FFFFFF;
      --bg-gray: #F5F5F5;
      --bg-card: #FFFFFF;
      
      /* 文本颜色 */
      --text-primary: #333333;
      --text-secondary: #666666;
      --text-hint: #999999;
      
      /* 边框与阴影 */
      --border-light: #EEEEEE;
      --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.1);
      
      /* 间距 */
      --space-xs: 4px;
      --space-sm: 8px;
      --space-md: 16px;
      --space-lg: 24px;
      --space-xl: 32px;
      
      /* 圆角 */
      --radius-sm: 4px;
      --radius-md: 8px;
      --radius-lg: 16px;
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
    }
    
    body {
      background-color: var(--bg-gray);
      color: var(--text-primary);
      line-height: 1.5;
    }
    
    .app-container {
      max-width: 480px;
      margin: 0 auto;
      background: var(--bg-light);
      min-height: 100vh;
    }
    
    .header {
      padding: var(--space-md);
      background: var(--bg-light);
      border-bottom: 1px solid var(--border-light);
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: sticky;
      top: 0;
      z-index: 100;
    }
    
    .header-title {
      font-size: 18px;
      font-weight: 500;
    }
    
    .content {
      padding: var(--space-md);
    }
    
    .card {
      background: var(--bg-card);
      border-radius: var(--radius-md);
      padding: var(--space-md);
      margin-bottom: var(--space-md);
      box-shadow: var(--shadow-card);
    }
    
    .button {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: var(--space-md) var(--space-lg);
      border-radius: var(--radius-md);
      font-weight: 500;
      border: none;
      cursor: pointer;
      transition: all 0.2s;
    }
    
    .button.primary {
      background: var(--primary);
      color: white;
    }
    
    .button.primary:hover {
      background: var(--primary-dark);
    }
    
    .button.outline {
      border: 1px solid var(--primary);
      color: var(--primary);
      background: transparent;
    }
    
    .button.outline:hover {
      background: rgba(255, 107, 149, 0.1);
    }
    
    .nav-bar {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background: var(--bg-light);
      display: flex;
      justify-content: space-around;
      padding: var(--space-sm) 0;
      border-top: 1px solid var(--border-light);
    }
    
    .nav-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-decoration: none;
      color: var(--text-hint);
      font-size: 12px;
    }
    
    .nav-item.active {
      color: var(--primary);
    }
    
    .nav-icon {
      font-size: 24px;
      margin-bottom: var(--space-xs);
    }
    
    .template-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: var(--space-md);
      margin-bottom: var(--space-lg);
    }
    
    .template-item {
      border-radius: var(--radius-md);
      overflow: hidden;
      position: relative;
      aspect-ratio: 3/4;
      background: var(--bg-card);
      box-shadow: var(--shadow-card);
    }
    
    .template-preview {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .template-name {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: var(--space-sm);
      background: rgba(255, 255, 255, 0.9);
      font-size: 14px;
      text-align: center;
    }
    
    .form-group {
      margin-bottom: var(--space-lg);
    }
    
    .form-label {
      display: block;
      margin-bottom: var(--space-sm);
      color: var(--text-secondary);
      font-weight: 500;
    }
    
    .form-input {
      width: 100%;
      padding: var(--space-md);
      border: 1px solid var(--border-light);
      border-radius: var(--radius-md);
      font-size: 16px;
    }
    
    .form-input:focus {
      outline: none;
      border-color: var(--primary);
    }
    
    .tabs {
      display: flex;
      margin-bottom: var(--space-lg);
      border-bottom: 1px solid var(--border-light);
    }
    
    .tab {
      padding: var(--space-md);
      color: var(--text-secondary);
      border-bottom: 2px solid transparent;
      cursor: pointer;
    }
    
    .tab.active {
      color: var(--primary);
      border-bottom-color: var(--primary);
    }
    
    .order-item {
      display: flex;
      align-items: center;
      padding: var(--space-md);
      border-bottom: 1px solid var(--border-light);
    }
    
    .order-info {
      flex: 1;
      margin-left: var(--space-md);
    }
    
    .order-title {
      font-weight: 500;
      margin-bottom: var(--space-xs);
    }
    
    .order-meta {
      font-size: 14px;
      color: var(--text-secondary);
    }
    
    .status-badge {
      padding: var(--space-xs) var(--space-sm);
      border-radius: var(--radius-sm);
      font-size: 12px;
      font-weight: 500;
    }
    
    .status-badge.success {
      background: rgba(76, 175, 80, 0.1);
      color: var(--accent-green);
    }
    
    .status-badge.pending {
      background: rgba(33, 150, 243, 0.1);
      color: var(--accent-blue);
    }
    
    .floating-button {
      position: fixed;
      bottom: 80px;
      right: var(--space-md);
      width: 56px;
      height: 56px;
      border-radius: 28px;
      background: var(--primary);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 12px rgba(255, 107, 149, 0.4);
      border: none;
      cursor: pointer;
    }
    
    .floating-button:hover {
      background: var(--primary-dark);
    }
    
    .preview-card {
      aspect-ratio: 3/4;
      background: white;
      border-radius: var(--radius-md);
      box-shadow: var(--shadow-card);
      margin-bottom: var(--space-lg);
      position: relative;
      overflow: hidden;
    }
    
    .preview-content {
      position: absolute;
      inset: 0;
      padding: var(--space-lg);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
    }
    
    .toolbar {
      display: flex;
      gap: var(--space-md);
      margin-bottom: var(--space-lg);
      overflow-x: auto;
      padding-bottom: var(--space-sm);
    }
    
    .tool-button {
      padding: var(--space-sm) var(--space-md);
      border: 1px solid var(--border-light);
      border-radius: var(--radius-md);
      background: white;
      color: var(--text-secondary);
      font-size: 14px;
      white-space: nowrap;
    }
    
    .tool-button.active {
      background: var(--primary);
      color: white;
      border-color: var(--primary);
    }
  </style>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body>

<div class="app-container">
  <!-- 首页/模板选择 -->
  <div id="home">
    <div class="header">
      <div class="header-title">选择贺卡模板</div>
      <button class="icon-button">
        <span class="material-icons">search</span>
      </button>
    </div>
    
    <div class="content">
      <div class="tabs">
        <div class="tab active">推荐</div>
        <div class="tab">节日</div>
        <div class="tab">生日</div>
        <div class="tab">爱情</div>
        <div class="tab">感谢</div>
      </div>
      
      <div class="template-grid">
        <a href="#edit" class="template-item">
          <img src="template1.jpg" class="template-preview">
          <div class="template-name">浪漫玫瑰</div>
        </a>
        <a href="#edit" class="template-item">
          <img src="template2.jpg" class="template-preview">
          <div class="template-name">清新向日葵</div>
        </a>
        <a href="#edit" class="template-item">
          <img src="template3.jpg" class="template-preview">
          <div class="template-name">生日快乐</div>
        </a>
        <a href="#edit" class="template-item">
          <img src="template4.jpg" class="template-preview">
          <div class="template-name">感恩有你</div>
        </a>
      </div>
      
      <div class="card">
        <h3 style="margin-bottom: var(--space-md);">常用模板</h3>
        <div class="template-grid" style="grid-template-columns: repeat(3, 1fr);">
          <div class="template-item" style="aspect-ratio: 1;">
            <img src="template5.jpg" class="template-preview">
          </div>
          <div class="template-item" style="aspect-ratio: 1;">
            <img src="template6.jpg" class="template-preview">
          </div>
          <div class="template-item" style="aspect-ratio: 1;">
            <img src="template7.jpg" class="template-preview">
          </div>
        </div>
      </div>
    </div>
    
    <div class="nav-bar">
      <a href="#home" class="nav-item active">
        <span class="material-icons nav-icon">dashboard</span>
        <span>模板</span>
      </a>
      <a href="#orders" class="nav-item">
        <span class="material-icons nav-icon">receipt</span>
        <span>订单</span>
      </a>
      <a href="#printer" class="nav-item">
        <span class="material-icons nav-icon">print</span>
        <span>打印机</span>
      </a>
      <a href="#settings" class="nav-item">
        <span class="material-icons nav-icon">settings</span>
        <span>设置</span>
      </a>
    </div>
  </div>
  
  <!-- 编辑贺卡页面 -->
  <div id="edit" style="display: none;">
    <div class="header">
      <button class="icon-button" onclick="window.location.href='#home'">
        <span class="material-icons">arrow_back</span>
      </button>
      <div class="header-title">编辑贺卡</div>
      <button class="icon-button">
        <span class="material-icons">more_vert</span>
      </button>
    </div>
    
    <div class="content">
      <div class="preview-card">
        <img src="template1.jpg" style="width: 100%; height: 100%; object-fit: cover;">
        <div class="preview-content">
          <div style="font-size: 24px; margin-bottom: var(--space-md);">生日快乐</div>
          <div style="color: var(--text-secondary);">愿你的生活如花般绚丽多彩</div>
        </div>
      </div>
      
      <div class="toolbar">
        <button class="tool-button active">文字</button>
        <button class="tool-button">字体</button>
        <button class="tool-button">颜色</button>
        <button class="tool-button">大小</button>
        <button class="tool-button">对齐</button>
        <button class="tool-button">装饰</button>
      </div>
      
      <div class="form-group">
        <label class="form-label">标题</label>
        <input type="text" class="form-input" value="生日快乐">
      </div>
      
      <div class="form-group">
        <label class="form-label">祝福语</label>
        <textarea class="form-input" rows="4">愿你的生活如花般绚丽多彩</textarea>
      </div>
      
      <div class="form-group">
        <label class="form-label">落款</label>
        <input type="text" class="form-input" placeholder="输入署名">
      </div>
      
      <button class="button primary" style="width: 100%;">
        <span class="material-icons" style="margin-right: var(--space-sm);">print</span>
        打印贺卡
      </button>
    </div>
  </div>
  
  <!-- 订单记录页面 -->
  <div id="orders" style="display: none;">
    <div class="header">
      <div class="header-title">打印记录</div>
      <button class="icon-button">
        <span class="material-icons">filter_list</span>
      </button>
    </div>
    
    <div class="content">
      <div class="tabs">
        <div class="tab active">全部</div>
        <div class="tab">今天</div>
        <div class="tab">本周</div>
        <div class="tab">本月</div>
      </div>
      
      <div class="order-item">
        <img src="template1.jpg" style="width: 60px; height: 80px; object-fit: cover; border-radius: var(--radius-sm);">
        <div class="order-info">
          <div class="order-title">浪漫玫瑰贺卡</div>
          <div class="order-meta">
            2024-01-20 14:30
            <span style="margin: 0 var(--space-xs);">•</span>
            已打印
          </div>
        </div>
        <span class="status-badge success">完成</span>
      </div>
      
      <div class="order-item">
        <img src="template2.jpg" style="width: 60px; height: 80px; object-fit: cover; border-radius: var(--radius-sm);">
        <div class="order-info">
          <div class="order-title">生日祝福卡</div>
          <div class="order-meta">
            2024-01-20 13:15
            <span style="margin: 0 var(--space-xs);">•</span>
            打印中
          </div>
        </div>
        <span class="status-badge pending">进行中</span>
      </div>
      
      <div class="order-item">
        <img src="template3.jpg" style="width: 60px; height: 80px; object-fit: cover; border-radius: var(--radius-sm);">
        <div class="order-info">
          <div class="order-title">感恩贺卡</div>
          <div class="order-meta">
            2024-01-20 11:45
            <span style="margin: 0 var(--space-xs);">•</span>
            已打印
          </div>
        </div>
        <span class="status-badge success">完成</span>
      </div>
    </div>
    
    <button class="floating-button">
      <span class="material-icons">add</span>
    </button>
    
    <div class="nav-bar">
      <a href="#home" class="nav-item">
        <span class="material-icons nav-icon">dashboard</span>
        <span>模板</span>
      </a>
      <a href="#orders" class="nav-item active">
        <span class="material-icons nav-icon">receipt</span>
        <span>订单</span>
      </a>
      <a href="#printer" class="nav-item">
        <span class="material-icons nav-icon">print</span>
        <span>打印机</span>
      </a>
      <a href="#settings" class="nav-item">
        <span class="material-icons nav-icon">settings</span>
        <span>设置</span>
      </a>
    </div>
  </div>
  
  <!-- 打印机管理页面 -->
  <div id="printer" style="display: none;">
    <div class="header">
      <div class="header-title">打印机管理</div>
      <button class="icon-button">
        <span class="material-icons">add</span>
      </button>
    </div>
    
    <div class="content">
      <div class="card">
        <div style="display: flex; align-items: center; margin-bottom: var(--space-md);">
          <span class="material-icons" style="font-size: 36px; color: var(--accent-blue); margin-right: var(--space-md);">print</span>
          <div style="flex: 1;">
            <div style="font-weight: 500;">EPSON L805</div>
            <div style="font-size: 14px; color: var(--text-secondary);">已连接 - USB</div>
          </div>
          <span class="material-icons" style="color: var(--accent-green);">check_circle</span>
        </div>
        
        <div style="display: flex; gap: var(--space-md);">
          <button class="button outline" style="flex: 1;">
            <span class="material-icons" style="margin-right: var(--space-xs);">settings</span>
            设置
          </button>
          <button class="button outline" style="flex: 1;">
            <span class="material-icons" style="margin-right: var(--space-xs);">print</span>
            测试页
          </button>
        </div>
      </div>
      
      <div class="card">
        <h3 style="margin-bottom: var(--space-md);">打印设置</h3>
        
        <div class="form-group">
          <label class="form-label">纸张大小</label>
          <select class="form-input">
            <option>A6 (148 × 105mm)</option>
            <option>A5 (210 × 148mm)</option>
            <option>自定义大小</option>
          </select>
        </div>
        
        <div class="form-group">
          <label class="form-label">打印质量</label>
          <select class="form-input">
            <option>标准</option>
            <option>照片质量</option>
            <option>经济模式</option>
          </select>
        </div>
        
        <div class="form-group">
          <label class="form-label">边距</label>
          <input type="range" class="form-input">
        </div>
      </div>
      
      <div class="card">
        <h3 style="margin-bottom: var(--space-md);">打印机状态</h3>
        
        <div style="display: flex; margin-bottom: var(--space-md);">
          <div style="flex: 1;">
            <div style="font-size: 14px; color: var(--text-secondary);">黑色墨水</div>
            <div style="height: 4px; background: var(--border-light); border-radius: 2px; margin-top: var(--space-xs);">
              <div style="width: 75%; height: 100%; background: var(--text-primary); border-radius: 2px;"></div>
            </div>
          </div>
          <div style="margin-left: var(--space-md);">75%</div>
        </div>
        
        <div style="display: flex; margin-bottom: var(--space-md);">
          <div style="flex: 1;">
            <div style="font-size: 14px; color: var(--text-secondary);">彩色墨水</div>
            <div style="height: 4px; background: var(--border-light); border-radius: 2px; margin-top: var(--space-xs);">
              <div style="width: 60%; height: 100%; background: var(--primary); border-radius: 2px;"></div>
            </div>
          </div>
          <div style="margin-left: var(--space-md);">60%</div>
        </div>
        
        <div style="display: flex; margin-bottom: var(--space-md);">
          <div style="flex: 1;">
            <div style="font-size: 14px; color: var(--text-secondary);">纸张</div>
            <div style="height: 4px; background: var(--border-light); border-radius: 2px; margin-top: var(--space-xs);">
              <div style="width: 90%; height: 100%; background: var(--accent-green); border-radius: 2px;"></div>
            </div>
          </div>
          <div style="margin-left: var(--space-md);">90%</div>
        </div>
      </div>
    </div>
    
    <div class="nav-bar">
      <a href="#home" class="nav-item">
        <span class="material-icons nav-icon">dashboard</span>
        <span>模板</span>
      </a>
      <a href="#orders" class="nav-item">
        <span class="material-icons nav-icon">receipt</span>
        <span>订单</span>
      </a>
      <a href="#printer" class="nav-item active">
        <span class="material-icons nav-icon">print</span>
        <span>打印机</span>
      </a>
      <a href="#settings" class="nav-item">
        <span class="material-icons nav-icon">settings</span>
        <span>设置</span>
      </a>
    </div>
  </div>
  
  <!-- 设置页面 -->
  <div id="settings" style="display: none;">
    <div class="header">
      <div class="header-title">设置</div>
    </div>
    
    <div class="content">
      <div class="card">
        <div style="display: flex; align-items: center; margin-bottom: var(--space-lg);">
          <img src="avatar.jpg" style="width: 60px; height: 60px; border-radius: 30px; margin-right: var(--space-md);">
          <div>
            <div style="font-weight: 500;">花店名称</div>
            <div style="font-size: 14px; color: var(--text-secondary);">店员账号</div>
          </div>
        </div>
        
        <button class="button outline" style="width: 100%;">
          <span class="material-icons" style="margin-right: var(--space-sm);">edit</span>
          编辑资料
        </button>
      </div>
      
      <div class="card">
        <h3 style="margin-bottom: var(--space-md);">通用设置</h3>
        
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--space-md);">
          <div>
            <div>自动保存</div>
            <div style="font-size: 14px; color: var(--text-secondary);">编辑时自动保存更改</div>
          </div>
          <label class="switch">
            <input type="checkbox" checked>
            <span class="slider"></span>
          </label>
        </div>
        
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--space-md);">
          <div>
            <div>打印预览</div>
            <div style="font-size: 14px; color: var(--text-secondary);">打印前显示预览窗口</div>
          </div>
          <label class="switch">
            <input type="checkbox" checked>
            <span class="slider"></span>
          </label>
        </div>
        
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <div>
            <div>离线模式</div>
            <div style="font-size: 14px; color: var(--text-secondary);">允许离线编辑贺卡</div>
          </div>
          <label class="switch">
            <input type="checkbox">
            <span class="slider"></span>
          </label>
        </div>
      </div>
      
      <div class="card">
        <h3 style="margin-bottom: var(--space-md);">数据管理</h3>
        
        <button class="button outline" style="width: 100%; margin-bottom: var(--space-md);">
          <span class="material-icons" style="margin-right: var(--space-sm);">backup</span>
          备份数据
        </button>
        
        <button class="button outline" style="width: 100%; margin-bottom: var(--space-md);">
          <span class="material-icons" style="margin-right: var(--space-sm);">restore</span>
          恢复数据
        </button>
        
        <button class="button outline" style="width: 100%;">
          <span class="material-icons" style="margin-right: var(--space-sm);">delete</span>
          清除缓存
        </button>
      </div>
      
      <div class="card">
        <h3 style="margin-bottom: var(--space-md);">关于</h3>
        
        <div style="margin-bottom: var(--space-md);">
          <div style="font-size: 14px; color: var(--text-secondary);">版本</div>
        </div>
      </div>
    </div>
  </div>
</div>

</body>
</html> 