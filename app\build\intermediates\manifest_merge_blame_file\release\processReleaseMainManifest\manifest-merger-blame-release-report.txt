1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.greetingcardprinter"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="29"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\CardPrint3\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\CardPrint3\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->D:\CardPrint3\app\src\main\AndroidManifest.xml:6:5-79
12-->D:\CardPrint3\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
13-->D:\CardPrint3\app\src\main\AndroidManifest.xml:7:5-76
13-->D:\CardPrint3\app\src\main\AndroidManifest.xml:7:22-73
14    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
14-->D:\CardPrint3\app\src\main\AndroidManifest.xml:8:5-76
14-->D:\CardPrint3\app\src\main\AndroidManifest.xml:8:22-73
15    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
15-->D:\CardPrint3\app\src\main\AndroidManifest.xml:9:5-79
15-->D:\CardPrint3\app\src\main\AndroidManifest.xml:9:22-76
16    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
16-->D:\CardPrint3\app\src\main\AndroidManifest.xml:10:5-81
16-->D:\CardPrint3\app\src\main\AndroidManifest.xml:10:22-78
17    <uses-permission android:name="android.permission.NEARBY_WIFI_DEVICES" />
17-->D:\CardPrint3\app\src\main\AndroidManifest.xml:11:5-78
17-->D:\CardPrint3\app\src\main\AndroidManifest.xml:11:22-75
18    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
18-->D:\CardPrint3\app\src\main\AndroidManifest.xml:12:5-80
18-->D:\CardPrint3\app\src\main\AndroidManifest.xml:12:22-77
19    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
19-->D:\CardPrint3\app\src\main\AndroidManifest.xml:13:5-81
19-->D:\CardPrint3\app\src\main\AndroidManifest.xml:13:22-78
20    <uses-permission android:name="android.permission.READ_CLIPBOARD" />
20-->D:\CardPrint3\app\src\main\AndroidManifest.xml:14:5-73
20-->D:\CardPrint3\app\src\main\AndroidManifest.xml:14:22-70
21
22    <!-- 网络服务发现权限 -->
23    <uses-permission android:name="android.permission.NFC" />
23-->D:\CardPrint3\app\src\main\AndroidManifest.xml:17:5-62
23-->D:\CardPrint3\app\src\main\AndroidManifest.xml:17:22-59
24    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
24-->D:\CardPrint3\app\src\main\AndroidManifest.xml:18:5-79
24-->D:\CardPrint3\app\src\main\AndroidManifest.xml:18:22-76
25
26    <!-- Android 13+ 需要的权限 -->
27    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
27-->D:\CardPrint3\app\src\main\AndroidManifest.xml:21:5-77
27-->D:\CardPrint3\app\src\main\AndroidManifest.xml:21:22-74
28
29    <permission
29-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9653369f29d51ad73d9709a09e371dee\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
30        android:name="com.example.greetingcardprinter.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
30-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9653369f29d51ad73d9709a09e371dee\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
31        android:protectionLevel="signature" />
31-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9653369f29d51ad73d9709a09e371dee\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
32
33    <uses-permission android:name="com.example.greetingcardprinter.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
33-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9653369f29d51ad73d9709a09e371dee\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
33-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9653369f29d51ad73d9709a09e371dee\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
34
35    <application
35-->D:\CardPrint3\app\src\main\AndroidManifest.xml:23:5-67:19
36        android:allowBackup="true"
36-->D:\CardPrint3\app\src\main\AndroidManifest.xml:24:9-35
37        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
37-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9653369f29d51ad73d9709a09e371dee\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
38        android:dataExtractionRules="@xml/data_extraction_rules"
38-->D:\CardPrint3\app\src\main\AndroidManifest.xml:25:9-65
39        android:extractNativeLibs="false"
40        android:fullBackupContent="@xml/backup_rules"
40-->D:\CardPrint3\app\src\main\AndroidManifest.xml:26:9-54
41        android:icon="@mipmap/ic_launcher"
41-->D:\CardPrint3\app\src\main\AndroidManifest.xml:27:9-43
42        android:label="@string/app_name"
42-->D:\CardPrint3\app\src\main\AndroidManifest.xml:28:9-41
43        android:roundIcon="@mipmap/ic_launcher_round"
43-->D:\CardPrint3\app\src\main\AndroidManifest.xml:29:9-54
44        android:supportsRtl="true"
44-->D:\CardPrint3\app\src\main\AndroidManifest.xml:30:9-35
45        android:theme="@style/Theme.CardPrint" >
45-->D:\CardPrint3\app\src\main\AndroidManifest.xml:31:9-47
46        <activity
46-->D:\CardPrint3\app\src\main\AndroidManifest.xml:34:9-41:20
47            android:name="com.example.greetingcardprinter.activities.MainActivity"
47-->D:\CardPrint3\app\src\main\AndroidManifest.xml:35:13-52
48            android:exported="true" >
48-->D:\CardPrint3\app\src\main\AndroidManifest.xml:36:13-36
49            <intent-filter>
49-->D:\CardPrint3\app\src\main\AndroidManifest.xml:37:13-40:29
50                <action android:name="android.intent.action.MAIN" />
50-->D:\CardPrint3\app\src\main\AndroidManifest.xml:38:17-69
50-->D:\CardPrint3\app\src\main\AndroidManifest.xml:38:25-66
51
52                <category android:name="android.intent.category.LAUNCHER" />
52-->D:\CardPrint3\app\src\main\AndroidManifest.xml:39:17-77
52-->D:\CardPrint3\app\src\main\AndroidManifest.xml:39:27-74
53            </intent-filter>
54        </activity>
55        <activity
55-->D:\CardPrint3\app\src\main\AndroidManifest.xml:43:9-46:39
56            android:name="com.example.greetingcardprinter.PrintSizeTestActivity"
56-->D:\CardPrint3\app\src\main\AndroidManifest.xml:44:13-50
57            android:exported="true"
57-->D:\CardPrint3\app\src\main\AndroidManifest.xml:46:13-36
58            android:label="打印尺寸测试" />
58-->D:\CardPrint3\app\src\main\AndroidManifest.xml:45:13-35
59        <activity
59-->D:\CardPrint3\app\src\main\AndroidManifest.xml:48:9-51:40
60            android:name="com.example.greetingcardprinter.activities.ImagePickerActivity"
60-->D:\CardPrint3\app\src\main\AndroidManifest.xml:49:13-59
61            android:exported="false"
61-->D:\CardPrint3\app\src\main\AndroidManifest.xml:51:13-37
62            android:label="选择图片" />
62-->D:\CardPrint3\app\src\main\AndroidManifest.xml:50:13-33
63        <activity
63-->D:\CardPrint3\app\src\main\AndroidManifest.xml:53:9-56:40
64            android:name="com.example.greetingcardprinter.activities.TemplateManagerActivity"
64-->D:\CardPrint3\app\src\main\AndroidManifest.xml:54:13-63
65            android:exported="false"
65-->D:\CardPrint3\app\src\main\AndroidManifest.xml:56:13-37
66            android:label="模板管理" />
66-->D:\CardPrint3\app\src\main\AndroidManifest.xml:55:13-33
67
68        <provider
69            android:name="androidx.core.content.FileProvider"
69-->D:\CardPrint3\app\src\main\AndroidManifest.xml:59:13-62
70            android:authorities="com.example.greetingcardprinter.fileprovider"
70-->D:\CardPrint3\app\src\main\AndroidManifest.xml:60:13-64
71            android:exported="false"
71-->D:\CardPrint3\app\src\main\AndroidManifest.xml:61:13-37
72            android:grantUriPermissions="true" >
72-->D:\CardPrint3\app\src\main\AndroidManifest.xml:62:13-47
73            <meta-data
73-->D:\CardPrint3\app\src\main\AndroidManifest.xml:63:13-65:54
74                android:name="android.support.FILE_PROVIDER_PATHS"
74-->D:\CardPrint3\app\src\main\AndroidManifest.xml:64:17-67
75                android:resource="@xml/file_paths" />
75-->D:\CardPrint3\app\src\main\AndroidManifest.xml:65:17-51
76        </provider>
77        <provider
77-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99a5bec0319d01b025141ac7c6c65239\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
78            android:name="androidx.startup.InitializationProvider"
78-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99a5bec0319d01b025141ac7c6c65239\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
79            android:authorities="com.example.greetingcardprinter.androidx-startup"
79-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99a5bec0319d01b025141ac7c6c65239\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
80            android:exported="false" >
80-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99a5bec0319d01b025141ac7c6c65239\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
81            <meta-data
81-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99a5bec0319d01b025141ac7c6c65239\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
82                android:name="androidx.emoji2.text.EmojiCompatInitializer"
82-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99a5bec0319d01b025141ac7c6c65239\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
83                android:value="androidx.startup" />
83-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99a5bec0319d01b025141ac7c6c65239\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
84            <meta-data
84-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0c8f465de314c122842236e7cbdaa66\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
85                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
85-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0c8f465de314c122842236e7cbdaa66\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
86                android:value="androidx.startup" />
86-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0c8f465de314c122842236e7cbdaa66\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
87            <meta-data
87-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
88                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
88-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
89                android:value="androidx.startup" />
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
90        </provider>
91
92        <receiver
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
93            android:name="androidx.profileinstaller.ProfileInstallReceiver"
93-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
94            android:directBootAware="false"
94-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
95            android:enabled="true"
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
96            android:exported="true"
96-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
97            android:permission="android.permission.DUMP" >
97-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
98            <intent-filter>
98-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
99                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
99-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
99-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
100            </intent-filter>
101            <intent-filter>
101-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
102                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
102-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
102-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
103            </intent-filter>
104            <intent-filter>
104-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
105                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
105-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
105-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
106            </intent-filter>
107            <intent-filter>
107-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
108                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
108-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
108-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1f237a2d091567fea8962dd1354bc14\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
109            </intent-filter>
110        </receiver>
111    </application>
112
113</manifest>
