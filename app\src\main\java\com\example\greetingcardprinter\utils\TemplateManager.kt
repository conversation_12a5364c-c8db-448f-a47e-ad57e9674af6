package com.example.greetingcardprinter.utils

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.example.greetingcardprinter.models.Template
import com.example.greetingcardprinter.models.TextAlignment
import com.example.greetingcardprinter.models.TextLine
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * 模板管理器
 * 负责模板的本地存储和管理，使用SharedPreferences + JSON序列化
 */
class TemplateManager(context: Context) {
    
    companion object {
        private const val TAG = "TemplateManager"
        private const val PREF_NAME = "template_preferences"
        private const val KEY_TEMPLATES = "templates"
    }
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()
    
    /**
     * 保存模板
     */
    fun saveTemplate(template: Template): Boolean {
        return try {
            val templates = getAllTemplates().toMutableList()
            
            // 检查是否已存在同ID的模板，如果存在则更新，否则添加
            val existingIndex = templates.indexOfFirst { it.id == template.id }
            if (existingIndex >= 0) {
                templates[existingIndex] = template
                Log.d(TAG, "更新模板: ${template.name}")
            } else {
                templates.add(template)
                Log.d(TAG, "添加新模板: ${template.name}")
            }
            
            saveTemplateList(templates)
            true
        } catch (e: Exception) {
            Log.e(TAG, "保存模板失败", e)
            false
        }
    }
    
    /**
     * 获取所有模板
     */
    fun getAllTemplates(): List<Template> {
        return try {
            val templatesJson = sharedPreferences.getString(KEY_TEMPLATES, "[]")
            val type = object : TypeToken<List<Template>>() {}.type
            gson.fromJson(templatesJson, type) ?: emptyList()
        } catch (e: Exception) {
            Log.e(TAG, "获取模板列表失败", e)
            emptyList()
        }
    }
    
    /**
     * 根据ID获取模板
     */
    fun getTemplateById(id: String): Template? {
        return try {
            getAllTemplates().find { it.id == id }
        } catch (e: Exception) {
            Log.e(TAG, "获取模板失败: $id", e)
            null
        }
    }
    
    /**
     * 删除模板
     */
    fun deleteTemplate(id: String): Boolean {
        return try {
            val templates = getAllTemplates().toMutableList()
            val removed = templates.removeAll { it.id == id }
            if (removed) {
                saveTemplateList(templates)
                Log.d(TAG, "删除模板: $id")
            }
            removed
        } catch (e: Exception) {
            Log.e(TAG, "删除模板失败: $id", e)
            false
        }
    }
    
    /**
     * 更新模板
     */
    fun updateTemplate(template: Template): Boolean {
        return saveTemplate(template)
    }
    
    /**
     * 检查模板名称是否已存在
     */
    fun isTemplateNameExists(name: String, excludeId: String? = null): Boolean {
        return getAllTemplates().any { 
            it.name == name && it.id != excludeId 
        }
    }
    
    /**
     * 获取模板数量
     */
    fun getTemplateCount(): Int {
        return getAllTemplates().size
    }
    
    /**
     * 清空所有模板
     */
    fun clearAllTemplates(): Boolean {
        return try {
            sharedPreferences.edit().remove(KEY_TEMPLATES).apply()
            Log.d(TAG, "清空所有模板")
            true
        } catch (e: Exception) {
            Log.e(TAG, "清空模板失败", e)
            false
        }
    }
    
    /**
     * 保存模板列表到SharedPreferences
     */
    private fun saveTemplateList(templates: List<Template>): Boolean {
        return try {
            val templatesJson = gson.toJson(templates)
            sharedPreferences.edit().putString(KEY_TEMPLATES, templatesJson).apply()
            true
        } catch (e: Exception) {
            Log.e(TAG, "保存模板列表失败", e)
            false
        }
    }
    
    /**
     * 从文本内容和文本行创建模板
     */
    fun createTemplateFromContent(
        name: String,
        content: String,
        textLines: List<TextLine>,
        fontSize: Float,
        lineSpacing: Float
    ): Template {
        return Template(
            name = name,
            content = content,
            textLines = textLines,
            fontSize = fontSize,
            lineSpacing = lineSpacing
        )
    }
}
