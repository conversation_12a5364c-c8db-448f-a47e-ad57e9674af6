# 打印机连接问题修复说明

## 问题描述
应用在调用系统打印服务时一直找不到打印机，导致打印作业超时失败。

## 问题分析
通过日志分析发现以下问题：
1. **权限不足**：缺少网络服务发现所需的关键权限
2. **服务类型单一**：只使用`_ipp._tcp.`一种服务类型发现打印机
3. **系统打印服务未启用**：用户设备可能未启用系统打印服务
4. **网络发现失败**：在某些网络环境下，mDNS发现可能失败

## 修复方案

### 1. 权限修复 (AndroidManifest.xml)
添加了以下权限：
- `ACCESS_FINE_LOCATION` / `ACCESS_COARSE_LOCATION` - 网络发现需要位置权限
- `NEARBY_WIFI_DEVICES` - Android 13+需要的WiFi设备发现权限
- `CHANGE_WIFI_STATE` - WiFi状态变更权限
- `CHANGE_NETWORK_STATE` - 网络状态变更权限
- `POST_NOTIFICATIONS` - Android 13+通知权限

### 2. 多服务类型发现 (PrinterDiscoveryService.kt)
扩展了打印机发现机制：
- 支持多种服务类型：`_ipp._tcp.`, `_printer._tcp.`, `_http._tcp.`
- 添加打印机关键词过滤，提高发现准确性
- 实现智能缓存机制，防止打印机频繁丢失

### 3. 手动添加打印机功能
- 在打印机选择对话框中添加"手动添加"按钮
- 支持通过IP地址手动添加打印机
- 自动扫描常见打印机IP地址段

### 4. 系统打印服务检查 (PrintService.kt)
- 在打印前检查系统打印服务是否可用
- 自动引导用户启用打印服务
- 提供更详细的错误信息和解决建议

### 5. 增强的错误处理
- 改进打印作业监控机制
- 提供更友好的错误提示
- 添加自动重试和恢复机制

## 使用说明

### 自动发现打印机
1. 打开应用，点击打印机选择
2. 应用会自动发现网络中的打印机
3. 如果5秒内未发现，会自动扫描常见IP地址

### 手动添加打印机
1. 在打印机选择对话框中点击"手动添加"
2. 输入打印机的IP地址（如：*************）
3. 点击"添加"完成添加

### 启用系统打印服务
如果提示"系统打印服务不可用"：
1. 应用会自动打开系统设置
2. 在"打印"设置中启用相关打印服务
3. 返回应用重新尝试打印

## 常见问题解决

### Q: 仍然找不到打印机怎么办？
A: 
1. 确保手机和打印机在同一WiFi网络
2. 检查打印机是否支持网络打印（IPP/AirPrint）
3. 尝试手动添加打印机IP地址
4. 重启路由器和打印机

### Q: 打印作业一直显示"正在打印"？
A: 
1. 检查打印机是否有纸张和墨水
2. 查看打印机是否有错误指示灯
3. 重启打印机
4. 检查网络连接

### Q: 权限被拒绝怎么办？
A: 
1. 在系统设置中手动授予应用所需权限
2. 特别注意位置权限，这是网络发现的必需权限
3. 重新安装应用以重新请求权限

## 技术细节

### 网络发现机制
- 使用Android NSD (Network Service Discovery) API
- 支持mDNS/Bonjour协议
- 实现多线程并发发现

### 打印协议支持
- IPP (Internet Printing Protocol)
- HTTP打印
- 原始TCP打印（9100端口）

### 缓存策略
- 60秒打印机缓存，防止频繁丢失
- 智能重连机制
- 自动恢复断开的打印机连接

## 测试建议
1. 在不同网络环境下测试打印机发现
2. 测试手动添加各种品牌打印机
3. 验证权限请求和授予流程
4. 测试打印作业的完整生命周期
