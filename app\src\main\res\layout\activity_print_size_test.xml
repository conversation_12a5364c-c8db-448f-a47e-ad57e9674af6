<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <TextView
        android:id="@+id/titleText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="打印尺寸测试"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/descriptionText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="这是一个测试自定义打印纸张大小的页面。\n将生成一个114mm × 85mm的测试页面，包含边框和尺寸标注。"
        android:textSize="16sp"
        app:layout_constraintTop_toBottomOf="@id/titleText" />

    <Button
        android:id="@+id/printButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:text="打印测试页"
        app:layout_constraintTop_toBottomOf="@id/descriptionText" />

</androidx.constraintlayout.widget.ConstraintLayout> 