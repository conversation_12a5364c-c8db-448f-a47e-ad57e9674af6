package com.example.greetingcardprinter.services

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.pdf.PdfDocument
import android.os.CancellationSignal
import android.os.ParcelFileDescriptor
import android.print.PageRange
import android.print.PrintAttributes
import android.print.PrintDocumentAdapter
import android.print.PrintDocumentInfo
import android.print.PrintManager
import android.print.PrintJob
import android.print.PrintJobInfo
import android.util.Log
import com.example.greetingcardprinter.models.CardContent
import com.example.greetingcardprinter.models.PrinterInfo
import com.example.greetingcardprinter.models.TextAlignment
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.FileOutputStream
import java.io.IOException
import android.graphics.Typeface
import android.os.Bundle
import android.app.Activity
import java.util.concurrent.atomic.AtomicBoolean
import android.graphics.Bitmap
import android.text.TextPaint
import com.example.greetingcardprinter.utils.mmToPx
import android.text.StaticLayout
import android.text.Layout
import android.util.TypedValue
import com.example.greetingcardprinter.utils.dpToPx
import android.widget.Toast
import androidx.core.graphics.withSave
import com.example.greetingcardprinter.models.ImageContent
import com.example.greetingcardprinter.utils.EmojiUtils
import com.example.greetingcardprinter.utils.PrinterPreferences
import com.example.greetingcardprinter.models.ImagePosition

class PrintService(private val context: Context) {
    
    // 添加打印任务状态监听
    private var printJob: PrintJob? = null
    private val printJobStarted = AtomicBoolean(false)
    
    // 打印机配置管理器
    private val printerPreferences = PrinterPreferences(context)
    
    // 预缓存字体对象
    private val cachedTypeface by lazy {
        Typeface.createFromAsset(context.assets, "fonts/qiantuxianmoti.ttf")
    }
    
    // 预缓存画笔对象
    private val cachedTextPaint = TextPaint().apply {
        color = Color.BLACK
        isAntiAlias = true
        isSubpixelText = true  // 启用亚像素渲染
        flags = flags or Paint.ANTI_ALIAS_FLAG or Paint.FILTER_BITMAP_FLAG  // 启用抗锯齿和位图过滤
    }

    // 创建默认的打印属性，使用A4尺寸
    private val defaultPrintAttributes = PrintAttributes.Builder()
        .setMediaSize(PrintAttributes.MediaSize.ISO_A4)  // 使用标准A4尺寸
        .setColorMode(PrintAttributes.COLOR_MODE_COLOR)
        .setResolution(PrintAttributes.Resolution("default", "默认", 300, 300))
        .setMinMargins(PrintAttributes.Margins.NO_MARGINS)  // 先设置无边距，后面通过画布变换控制位置
        .build()
    
    // 取消打印作业
    fun cancelPrintJob() {
        printJob?.let { job ->
            if (job.info.state <= PrintJobInfo.STATE_STARTED) {
                job.cancel()
                Log.d(TAG, "Print job canceled")
            }
        }
        printJob = null
        printJobStarted.set(false)
    }

    // 添加获取PrintManager的方法，供MiuiEnhancedPrintService使用
    internal fun getPrintManager(): PrintManager {
        return context.getSystemService(Context.PRINT_SERVICE) as PrintManager
    }
    
    // 打印结束后调用此方法
    fun onPrintDone(success: Boolean) {
        printJob = null
        printJobStarted.set(false)
    }

    // 在Activity的onPause或onDestroy中调用
    fun onDestroy() {
        cancelPrintJob()
    }
    
    suspend fun printCard(
        content: CardContent,
        printerInfo: PrinterInfo
    ): Boolean = withContext(Dispatchers.Main) {
        try {
            // 添加日志，记录打印开始
            Log.d(TAG, "开始打印贺卡: 内容长度=${content.textLines.size}行")
            
            // 确保之前的打印作业被取消
            cancelPrintJob()
            
            if (context !is Activity) {
                Log.e(TAG, "Context必须是Activity才能打印，当前context类型: ${context.javaClass.name}")
                return@withContext false
            }
            
            val printManager = context.getSystemService(Context.PRINT_SERVICE) as PrintManager
            val jobName = "Greeting Card ${System.currentTimeMillis()}"
            
            val printAdapter = object : PrintDocumentAdapter() {
                override fun onLayout(
                    oldAttributes: PrintAttributes?,
                    newAttributes: PrintAttributes,
                    cancellationSignal: CancellationSignal,
                    callback: LayoutResultCallback,
                    extras: Bundle?
                ) {
                    if (cancellationSignal.isCanceled) {
                        callback.onLayoutCancelled()
                        return
                    }
                    
                    // 强制使用A4尺寸的打印属性
                    val forcedAttributes = PrintAttributes.Builder()
                        .setMediaSize(PrintAttributes.MediaSize.ISO_A4)
                        .setColorMode(newAttributes.colorMode ?: PrintAttributes.COLOR_MODE_COLOR)
                        .setResolution(newAttributes.resolution ?: PrintAttributes.Resolution("default", "默认", 300, 300))
                        .setMinMargins(PrintAttributes.Margins.NO_MARGINS)
                        .build()
                    
                    // 更新这里：告诉系统我们有content.copies页
                    val info = PrintDocumentInfo.Builder(jobName)
                        .setContentType(PrintDocumentInfo.CONTENT_TYPE_DOCUMENT)
                        .setPageCount(content.copies)
                        .build()
                    
                    callback.onLayoutFinished(info, !forcedAttributes.equals(oldAttributes))
                }
                
                override fun onWrite(
                    pages: Array<out PageRange>,
                    destination: ParcelFileDescriptor,
                    cancellationSignal: CancellationSignal,
                    callback: WriteResultCallback
                ) {
                    if (cancellationSignal.isCanceled) {
                        callback.onWriteCancelled()
                        return
                    }
                    
                    val document = PdfDocument()
                    
                    // 计算A4纸张的尺寸（像素）
                    val a4WidthMm = 210f
                    val a4HeightMm = 297f
                    val a4WidthPx = a4WidthMm.mmToPx(context).toInt()
                    val a4HeightPx = a4HeightMm.mmToPx(context).toInt()
                    
                    // 计算贺卡的尺寸（像素）
                    val cardWidthPx = content.cardWidth.mmToPx(context).toInt()
                    val cardHeightPx = content.cardHeight.mmToPx(context).toInt()
                    
                    // 计算贺卡在A4纸张上的位置
                    val leftMarginPx = ((a4WidthPx - cardWidthPx) / 2f).toInt()
                    val topMarginPx = (cardHeightPx+10f.mmToPx(context)).toInt()
                    
                    // 生成多个页面，每页内容相同（模拟多份）
                    val pageRanges = mutableListOf<PageRange>()
                    
                    // 查找需要生成的页范围
                    for (pageRange in pages) {
                        for (i in pageRange.start..minOf(pageRange.end, content.copies - 1)) {
                            val pageInfo = PdfDocument.PageInfo.Builder(a4WidthPx, a4HeightPx, i).create()
                        val page = document.startPage(pageInfo)
                            val canvas = page.canvas
                            
                            // 使用withSave优化画布状态管理
                            canvas.withSave {
                                // 清空画布（设置白色背景）
                                drawColor(Color.WHITE)
                                
                                // 移动画布到贺卡绘制位置
                                translate(leftMarginPx.toFloat(), topMarginPx.toFloat())
                                
                                // 绘制贺卡内容
                                drawCardOnCanvas(this, content, cardWidthPx.toFloat(), cardHeightPx.toFloat())
                            }
                        
                        document.finishPage(page)
                            pageRanges.add(PageRange(i, i))
                        }
                    }
                    
                    try {
                        document.writeTo(FileOutputStream(destination.fileDescriptor))
                        if (pageRanges.isNotEmpty()) {
                            callback.onWriteFinished(pageRanges.toTypedArray())
                        } else {
                        callback.onWriteFinished(arrayOf(PageRange.ALL_PAGES))
                        }
                    } catch (e: IOException) {
                        Log.e(TAG, "写入PDF文档失败", e)
                        callback.onWriteFailed(e.message)
                    } finally {
                        document.close()
                    }
                }
            }
            
            // 使用A4纸张尺寸启动打印作业
            val job = printManager.print(jobName, printAdapter, defaultPrintAttributes)
            
            if (job == null) {
                Log.e(TAG, "打印管理器返回了null打印作业")
                return@withContext false
            }
            
            // 保存打印作业引用
            printJob = job
            
            // 打印作业已开始
            printJobStarted.set(true)
            
            // 开始监控打印作业状态
            startPrintJobMonitoring(job)
            
            // 返回初步成功，表示打印任务已加入队列
            // 注意：真正的打印成功状态将由监控机制在确认后设置
            return@withContext true
            
        } catch (e: Exception) {
            Log.e(TAG, "打印贺卡时发生错误", e)
            Log.e(TAG, "错误详情: ${e.message}, 原因: ${e.cause?.message ?: "未知"}")
            return@withContext false
        }
    }
    
    /**
     * 开始监控打印作业状态
     * 重要：这个机制确保只有当打印真正成功时才通知应用
     */
    private fun startPrintJobMonitoring(job: PrintJob) {
        val handler = android.os.Handler(android.os.Looper.getMainLooper())
        val maxRetries = 40  // 最多检查40次（约20秒）
        var checkCount = 0
        var lastState = -1
        
        // 打印状态查询任务
        val runnable = object : Runnable {
            override fun run() {
                // 增加计数器
                checkCount++
                
                // 检查打印作业状态
                val state = job.info.state
                
                // 只在状态变化时记录日志，减少日志噪音
                if (state != lastState) {
                    lastState = state
                    val stateString = getPrintJobStateString(state)
                    Log.d(TAG, "打印作业状态变化: $stateString")
                }
                
                when (state) {
                    PrintJobInfo.STATE_COMPLETED -> {
                        // 打印真正完成
                        Log.d(TAG, "打印作业已完成 - 通知应用")
                        notifyPrintResult(true)
                        return
                    }
                    PrintJobInfo.STATE_FAILED -> {
                        // 打印失败
                        Log.e(TAG, "打印作业失败")
                        notifyPrintResult(false, "打印失败")
                        return
                    }
                    PrintJobInfo.STATE_CANCELED -> {
                        // 打印被取消
                        Log.d(TAG, "打印作业被取消")
                        notifyPrintResult(false, "打印已取消")
                        return
                    }
                    PrintJobInfo.STATE_BLOCKED -> {
                        // 打印机可能离线或遇到问题
                        if (checkCount % 5 == 0) {  // 每5次检查记录一次日志
                            Log.w(TAG, "打印作业被阻塞，可能是打印机离线、缺纸或其他问题")
                        }
                    }
                }
                
                // 如果达到最大重试次数但打印作业仍未成功
                if (checkCount >= maxRetries) {
                    Log.w(TAG, "打印作业监控超时，可能打印机未连接或出现其他问题")
                    notifyPrintResult(false, "打印状态未知，请检查打印机连接")
                    return
                }
                
                // 每500毫秒检查一次状态
                handler.postDelayed(this, 500)
            }
        }
        
        // 立即开始第一次检查
        handler.post(runnable)
    }
    
    /**
     * 通知打印结果
     * 此方法确保即使在Activity被销毁的情况下也不会崩溃
     */
    private fun notifyPrintResult(success: Boolean, message: String = "") {
        try {
            // 首先检查上下文是否有效
            if (context is Activity && !(context as Activity).isFinishing) {
                (context as? com.example.greetingcardprinter.activities.MainActivity)?.runOnUiThread {
                    try {
                        // 通知ViewModel更新状态
                        (context as? com.example.greetingcardprinter.activities.MainActivity)?.viewModel?.onPrintResult(success, message)
                    } catch (e: Exception) {
                        Log.e(TAG, "通知打印结果时发生异常", e)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "调用通知打印结果方法时发生异常", e)
        }
    }
    
    // 检查打印作业是否处于活动状态
    private fun isPrintJobActive(): Boolean {
        val job = printJob ?: return false
        val state = job.info.state
        
        if (state == PrintJobInfo.STATE_STARTED || 
            state == PrintJobInfo.STATE_QUEUED || 
            state == PrintJobInfo.STATE_BLOCKED) {
            printJobStarted.set(true)
            return true
        }
        
        return false
    }
    
    // 获取打印作业状态的字符串表示
    private fun getPrintJobStateString(state: Int): String {
        return when (state) {
            PrintJobInfo.STATE_CREATED -> "已创建"
            PrintJobInfo.STATE_QUEUED -> "已排队"
            PrintJobInfo.STATE_STARTED -> "已开始"
            PrintJobInfo.STATE_BLOCKED -> "已阻塞"
            PrintJobInfo.STATE_COMPLETED -> "已完成"
            PrintJobInfo.STATE_FAILED -> "失败"
            PrintJobInfo.STATE_CANCELED -> "已取消"
            else -> "未知状态: $state"
        }
    }
    
    // 绘制贺卡内容到位图
    private fun drawCard(content: CardContent, context: Context): Bitmap {
        // 创建位图
        val cardWidth = content.cardWidth.mmToPx(context)
        val cardHeight = content.cardHeight.mmToPx(context)
        val bitmap = Bitmap.createBitmap(cardWidth.toInt(), cardHeight.toInt(), Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        
        // 使用共享的绘制方法
        drawCardOnCanvas(canvas, content, cardWidth.toFloat(), cardHeight.toFloat())
        
        return bitmap
    }
    
    // 绘制贺卡内容到PDF页面的canvas上
    // 修改为internal可见性，让MiuiEnhancedPrintService可以调用
    internal fun drawCardOnCanvas(
        canvas: Canvas,
        content: CardContent,
        cardWidthPx: Float,
        cardHeightPx: Float
    ) {
        // 清空画布
        canvas.drawColor(Color.WHITE)

        // 复用缓存的画笔对象，只更新字体大小
        cachedTextPaint.apply {
            val fontSizeMm = content.fontSize * 0.35277778f
            textSize = fontSizeMm.mmToPx(context)
            typeface = cachedTypeface
        }

        // 使用毫米计算边距
        val marginMm = 4f
        val marginPx = marginMm.mmToPx(context).toInt()
        val availableWidth = cardWidthPx - (marginPx * 2)
        
        // 处理图片内容
        var startY = marginPx.toFloat()
        val imageContent = content.imageContent
        
        // 如果有图片且位置为顶部或全屏
        if (imageContent != null && (imageContent.position == ImagePosition.TOP || 
                                     imageContent.position == ImagePosition.FULL)) {
            // 绘制图片
            startY = drawImage(canvas, imageContent, cardWidthPx, startY)
            
            // 如果是全屏图片，则不绘制文本
            if (imageContent.position == ImagePosition.FULL) {
                return
            }
        }

        // 遍历文本行并绘制
        var currentY = startY
        content.textLines.forEach { textLine ->
            // 检测emoji位置
            val emojiPositions = EmojiUtils.findEmojiPositions(textLine.text)
            Log.d(TAG, "content.lineSpacing: ${content.lineSpacing}")
            if (emojiPositions.isEmpty()) {
                // 如果没有emoji，使用普通的StaticLayout绘制
                val layout = StaticLayout.Builder.obtain(
                    textLine.text,
                    0,
                    textLine.text.length,
                    cachedTextPaint,
                    availableWidth.toInt()
                ).setAlignment(
                    when (textLine.alignment) {
                        TextAlignment.LEFT -> Layout.Alignment.ALIGN_NORMAL
                        TextAlignment.CENTER -> Layout.Alignment.ALIGN_CENTER
                        TextAlignment.RIGHT -> Layout.Alignment.ALIGN_OPPOSITE
                    }
                ).setLineSpacing(0f, content.lineSpacing)
                 .setIncludePad(false)
                 .setHyphenationFrequency(Layout.HYPHENATION_FREQUENCY_NONE)
                 .setBreakStrategy(Layout.BREAK_STRATEGY_SIMPLE)
                 .build()

                // 计算水平位置
                val x = when (textLine.alignment) {
                    TextAlignment.LEFT -> marginPx.toFloat()
                    TextAlignment.CENTER -> cardWidthPx / 2
                    TextAlignment.RIGHT -> cardWidthPx - marginPx.toFloat()
                }

                // 计算画布偏移
                val translateX = when (textLine.alignment) {
                    TextAlignment.LEFT -> x
                    TextAlignment.CENTER -> x - (layout.width / 2f)
                    TextAlignment.RIGHT -> x - layout.width
                }

                canvas.withSave {
                    translate(translateX, currentY)
                    layout.draw(this)
                }
                
                currentY += layout.height
            } else {
                // 如果包含emoji，分段绘制文本和emoji
                var lastEnd = 0
                var currentX = when (textLine.alignment) {
                    TextAlignment.LEFT -> marginPx.toFloat()
                    TextAlignment.CENTER -> cardWidthPx / 2
                    TextAlignment.RIGHT -> cardWidthPx - marginPx.toFloat()
                }

                // 计算整行文本的宽度（包括emoji）
                val totalWidth = cachedTextPaint.measureText(textLine.text)
                if (textLine.alignment != TextAlignment.LEFT) {
                    currentX -= if (textLine.alignment == TextAlignment.CENTER) totalWidth / 2 else totalWidth
                }

                // 绘制文本和emoji
                emojiPositions.forEach { emojiPos ->
                    // 绘制emoji前的文本
                    if (emojiPos.start > lastEnd) {
                        val text = textLine.text.substring(lastEnd, emojiPos.start)
                        canvas.drawText(text, currentX, currentY + cachedTextPaint.textSize, cachedTextPaint)
                        currentX += cachedTextPaint.measureText(text)
                    }

                    // 尝试直接绘制emoji文本，避免bitmap转换过程中的问题
                    val originalTextSize = cachedTextPaint.textSize
                    
                    // 临时增大字体，确保emoji能够完整显示
                    cachedTextPaint.textSize = cachedTextPaint.textSize * 1.2f
                    
                    // 直接绘制emoji文本
                    canvas.drawText(
                        emojiPos.emoji,
                        currentX,
                        currentY + cachedTextPaint.textSize * 0.9f, // 适当下移以对齐文本
                        cachedTextPaint
                    )
                    
                    // 计算emoji宽度并更新水平位置
                    currentX += cachedTextPaint.measureText(emojiPos.emoji)
                    
                    // 恢复原始字体大小
                    cachedTextPaint.textSize = originalTextSize

                    lastEnd = emojiPos.end
                }

                // 绘制最后一个emoji后的文本
                if (lastEnd < textLine.text.length) {
                    val text = textLine.text.substring(lastEnd)
                    canvas.drawText(text, currentX, currentY + cachedTextPaint.textSize, cachedTextPaint)
                }

                // 使用CardContent中的行间距值
                currentY += cachedTextPaint.textSize * content.lineSpacing
            }
        }
        
        // 如果有图片且位置为底部
        if (imageContent != null && imageContent.position == ImagePosition.BOTTOM) {
            // 绘制底部图片
            drawImage(canvas, imageContent, cardWidthPx, currentY)
        }
    }
    
    /**
     * 绘制图片到Canvas
     * 
     * @param canvas 绘图画布
     * @param imageContent 图片内容
     * @param cardWidthPx 卡片宽度(像素)
     * @param startY 开始Y坐标(像素)
     * @return 图片结束后的Y坐标
     */
    private fun drawImage(
        canvas: Canvas,
        imageContent: ImageContent,
        cardWidthPx: Float,
        startY: Float
    ): Float {
        // 获取图片
        val bitmap = imageContent.bitmap ?: return startY
        
        // 计算图片尺寸(像素)
        val imageWidthPx = imageContent.widthMm.mmToPx(context)
        val imageHeightPx = imageContent.heightMm.mmToPx(context)
        
        // 计算边距(像素)
        val marginTopPx = imageContent.marginTopMm.mmToPx(context)
        val marginBottomPx = imageContent.marginBottomMm.mmToPx(context)
        
        // 计算图片位置
        val left = (cardWidthPx - imageWidthPx) / 2f
        val top = startY + marginTopPx
        
        // 创建目标矩形区域
        val destRect = android.graphics.RectF(
            left,
            top,
            left + imageWidthPx,
            top + imageHeightPx
        )
        
        // 使用画布的withSave扩展函数保存状态
        canvas.withSave {
            // 绘制图片到画布
            drawBitmap(bitmap, null, destRect, null)
        }
        
        // 返回图片底部的Y坐标
        return top + imageHeightPx + marginBottomPx
    }
    
    companion object {
        private const val TAG = "PrintService"
    }
}
