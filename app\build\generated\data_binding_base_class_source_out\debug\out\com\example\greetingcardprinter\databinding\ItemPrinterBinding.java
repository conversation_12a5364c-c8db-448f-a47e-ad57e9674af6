// Generated by view binder compiler. Do not edit!
package com.example.greetingcardprinter.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.greetingcardprinter.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemPrinterBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final TextView printerAddressText;

  @NonNull
  public final TextView printerNameText;

  private ItemPrinterBinding(@NonNull CardView rootView, @NonNull TextView printerAddressText,
      @NonNull TextView printerNameText) {
    this.rootView = rootView;
    this.printerAddressText = printerAddressText;
    this.printerNameText = printerNameText;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemPrinterBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemPrinterBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_printer, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemPrinterBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.printerAddressText;
      TextView printerAddressText = ViewBindings.findChildViewById(rootView, id);
      if (printerAddressText == null) {
        break missingId;
      }

      id = R.id.printerNameText;
      TextView printerNameText = ViewBindings.findChildViewById(rootView, id);
      if (printerNameText == null) {
        break missingId;
      }

      return new ItemPrinterBinding((CardView) rootView, printerAddressText, printerNameText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
